# 字体文件目录

这个目录用于存放项目所需的字体文件。

## 支持的字体格式

- TTF (TrueType Font)
- OTF (OpenType Font)

## 推荐的楷体字体

为了获得最佳的水印效果，建议使用以下楷体字体：

1. **华文楷体 (STKaiti)**
   - 文件名: STKaiti.ttf
   - 来源: macOS系统字体

2. **楷体 (KaiTi)**
   - 文件名: simkai.ttf 或 kaiti.ttf
   - 来源: Windows系统字体

3. **方正楷体**
   - 文件名: FZKTJW.ttf
   - 来源: 方正字库

## 字体安装说明

### Windows系统
1. 将字体文件复制到此目录
2. 或者安装到系统字体目录: `C:\Windows\Fonts\`

### macOS系统
1. 将字体文件复制到此目录
2. 或者安装到系统字体目录: `/Library/Fonts/` 或 `~/Library/Fonts/`

### Linux系统
1. 将字体文件复制到此目录
2. 或者安装到系统字体目录: `/usr/share/fonts/` 或 `~/.fonts/`

## 字体获取方式

1. **从系统复制**
   - Windows: 从 `C:\Windows\Fonts\` 复制楷体相关文件
   - macOS: 从 `/System/Library/Fonts/` 复制楷体相关文件

2. **在线下载**
   - 从合法的字体网站下载免费楷体字体
   - 注意版权问题，仅使用免费或已授权的字体

3. **程序自动检测**
   - 运行程序时会自动检测系统中的楷体字体
   - 如果找到合适的字体，会提示复制到此目录

## 注意事项

- 请确保使用的字体文件具有合法的使用权限
- 字体文件较大时可能影响程序启动速度
- 建议使用支持完整中文字符集的字体文件
- 如果没有楷体字体，程序会使用默认字体，但效果可能不如楷体

## 字体测试

运行以下命令测试字体是否正常工作：

```bash
python examples/demo.py
```

或者使用程序的字体检测功能：

```bash
python main.py
# 选择菜单项 "6. 检查字体状态"
```
