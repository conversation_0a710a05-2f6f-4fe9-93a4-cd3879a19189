# -*- coding: utf-8 -*-
"""
PDF水印工具安装脚本
自动安装所需依赖包
"""

import subprocess
import sys
import os


def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False


def install_requirements():
    """安装requirements.txt中的所有包"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到 {requirements_file} 文件")
        return False
    
    try:
        print("正在安装所有依赖包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        print("✓ 所有依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 安装依赖包失败: {e}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("警告: 建议使用Python 3.7或更高版本")
        return False
    
    print("✓ Python版本符合要求")
    return True


def check_pip():
    """检查pip是否可用"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "--version"])
        print("✓ pip可用")
        return True
    except subprocess.CalledProcessError:
        print("✗ pip不可用，请先安装pip")
        return False


def upgrade_pip():
    """升级pip"""
    try:
        print("正在升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip升级成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ pip升级失败: {e}")
        return False


def test_imports():
    """测试关键包的导入"""
    test_packages = [
        ("PyPDF2", "PyPDF2"),
        ("reportlab", "reportlab"),
        ("PIL", "Pillow"),
        ("fitz", "PyMuPDF"),
        ("fonttools", "fonttools")
    ]
    
    print("\n测试包导入...")
    success_count = 0
    
    for import_name, package_name in test_packages:
        try:
            __import__(import_name)
            print(f"✓ {package_name} 导入成功")
            success_count += 1
        except ImportError:
            print(f"✗ {package_name} 导入失败")
    
    print(f"\n导入测试结果: {success_count}/{len(test_packages)} 个包成功")
    return success_count == len(test_packages)


def main():
    """主安装函数"""
    print("PDF水印工具 - 依赖安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        print("请升级Python版本后重试")
        return False
    
    # 检查pip
    if not check_pip():
        print("请先安装pip")
        return False
    
    # 升级pip
    upgrade_pip()
    
    # 安装依赖
    print("\n开始安装依赖包...")
    if install_requirements():
        print("\n依赖安装完成")
    else:
        print("\n依赖安装失败，尝试单独安装...")
        
        # 核心包列表
        core_packages = [
            "PyPDF2>=3.0.1",
            "reportlab>=4.0.4",
            "Pillow>=10.0.0",
            "PyMuPDF>=1.23.0",
            "fonttools>=4.42.1"
        ]
        
        for package in core_packages:
            install_package(package)
    
    # 测试导入
    print("\n" + "=" * 50)
    if test_imports():
        print("\n✓ 所有依赖包安装成功！")
        print("\n现在可以运行程序:")
        print("  python main.py")
        print("\n或运行演示:")
        print("  python examples/demo.py")
        print("\n或运行测试:")
        print("  python tests/test_watermark.py")
        return True
    else:
        print("\n✗ 部分依赖包安装失败")
        print("\n请手动安装失败的包:")
        print("  pip install <包名>")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中出错: {e}")
        sys.exit(1)
