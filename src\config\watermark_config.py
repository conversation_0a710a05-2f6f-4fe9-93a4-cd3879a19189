# -*- coding: utf-8 -*-
"""
水印配置模块
定义水印的所有参数和配置选项
"""

import os
from dataclasses import dataclass
from typing import Tuple, List


@dataclass
class WatermarkConfig:
    """水印配置类"""
    
    # 水印内容
    text: str = "小红书ID：11491392150\n关注保研喵学姐\n更多免费资料分享"
    
    # 字体设置
    font_name: str = "楷体"  # 字体名称
    font_size: int = 24     # 字号 (从54改为24，减小字体)
    font_path: str = ""     # 字体文件路径（如果需要）

    # 外观设置
    rotation: float = 45.0   # 旋转角度（度）
    opacity: float = 0.08    # 不透明度（从0.2改为0.08，更透明）
    color: Tuple[int, int, int] = (180, 180, 180)  # RGB颜色 (更浅的灰色)

    # 位置和布局设置
    page_ratio: float = 0.8          # 相对页面比例 (从0.5改为0.8，更分散)
    rows_per_page: int = 5           # 一页的行数 (从3改为5，更多行更分散)
    vertical_center_offset: float = 11.0  # 垂直居中偏移（毫米）
    horizontal_align: str = "center"  # 水平对齐方式
    vertical_align: str = "center"    # 垂直对齐方式
    
    # 间距设置
    line_spacing: float = 1.5        # 行间距倍数
    margin_horizontal: float = 20.0  # 水平边距（毫米）
    margin_vertical: float = 20.0    # 垂直边距（毫米）
    
    def __post_init__(self):
        """初始化后的处理"""
        # 将文本按行分割
        self.text_lines = self.text.split('\n')
        
        # 设置默认字体路径
        if not self.font_path:
            self.font_path = self._get_default_font_path()
    
    def _get_default_font_path(self) -> str:
        """获取默认字体路径"""
        # Windows系统楷体路径
        windows_font_paths = [
            "C:/Windows/Fonts/simkai.ttf",
            "C:/Windows/Fonts/SIMKAI.TTF",
            "C:/Windows/Fonts/kaiti.ttf",
            "C:/Windows/Fonts/KAITI.TTF"
        ]
        
        # 项目字体目录
        project_font_dir = os.path.join(os.path.dirname(__file__), "../../fonts")
        project_font_paths = [
            os.path.join(project_font_dir, "simkai.ttf"),
            os.path.join(project_font_dir, "kaiti.ttf")
        ]
        
        # 检查字体文件是否存在
        all_paths = windows_font_paths + project_font_paths
        for path in all_paths:
            if os.path.exists(path):
                return path
        
        # 如果都不存在，返回空字符串，后续会使用默认字体
        return ""
    
    def get_text_lines(self) -> List[str]:
        """获取文本行列表"""
        return self.text_lines
    
    def get_opacity_255(self) -> int:
        """获取255范围的不透明度值"""
        return int(self.opacity * 255)
    
    def get_color_with_alpha(self) -> Tuple[int, int, int, int]:
        """获取带透明度的RGBA颜色"""
        return (*self.color, self.get_opacity_255())
    
    def mm_to_points(self, mm: float) -> float:
        """毫米转换为点（1毫米 = 2.834645669 点）"""
        return mm * 2.834645669
    
    def get_vertical_offset_points(self) -> float:
        """获取垂直偏移的点数"""
        return self.mm_to_points(self.vertical_center_offset)
    
    def get_margin_horizontal_points(self) -> float:
        """获取水平边距的点数"""
        return self.mm_to_points(self.margin_horizontal)
    
    def get_margin_vertical_points(self) -> float:
        """获取垂直边距的点数"""
        return self.mm_to_points(self.margin_vertical)
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        if not self.text.strip():
            return False
        
        if self.font_size <= 0:
            return False
        
        if not (0 <= self.opacity <= 1):
            return False
        
        if self.rows_per_page <= 0:
            return False
        
        if not (0 <= self.page_ratio <= 1):
            return False
        
        return True
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'text': self.text,
            'font_name': self.font_name,
            'font_size': self.font_size,
            'font_path': self.font_path,
            'rotation': self.rotation,
            'opacity': self.opacity,
            'color': self.color,
            'page_ratio': self.page_ratio,
            'rows_per_page': self.rows_per_page,
            'vertical_center_offset': self.vertical_center_offset,
            'horizontal_align': self.horizontal_align,
            'vertical_align': self.vertical_align,
            'line_spacing': self.line_spacing,
            'margin_horizontal': self.margin_horizontal,
            'margin_vertical': self.margin_vertical
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'WatermarkConfig':
        """从字典创建配置对象"""
        return cls(**config_dict)


# 默认配置实例
DEFAULT_WATERMARK_CONFIG = WatermarkConfig()
