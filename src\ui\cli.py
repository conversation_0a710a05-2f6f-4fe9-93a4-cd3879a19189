# -*- coding: utf-8 -*-
"""
命令行界面模块
提供用户交互界面
"""

import os
import sys
from typing import List, Optional
from ..config.watermark_config import WatermarkConfig, DEFAULT_WATERMARK_CONFIG
from ..core.pdf_processor import PDFProcessor
from ..utils.file_manager import FileManager
from ..utils.font_detector import FontDetector


class CLI:
    """命令行界面类"""
    
    def __init__(self):
        """初始化CLI"""
        self.config = DEFAULT_WATERMARK_CONFIG
        self.processor = PDFProcessor(self.config)
        self.file_manager = FileManager()
        self.font_detector = FontDetector()
        
    def run(self):
        """运行主程序"""
        self.print_welcome()
        self.check_environment()
        
        while True:
            try:
                choice = self.show_main_menu()
                
                if choice == '1':
                    self.process_single_file()
                elif choice == '2':
                    self.process_multiple_files()
                elif choice == '3':
                    self.process_directory()
                elif choice == '4':
                    self.show_watermark_config()
                elif choice == '5':
                    self.modify_watermark_config()
                elif choice == '6':
                    self.check_font_status()
                elif choice == '7':
                    self.show_help()
                elif choice == '0':
                    print("感谢使用PDF水印添加工具！")
                    break
                else:
                    print("无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"程序运行出错: {e}")
                input("按回车键继续...")
    
    def print_welcome(self):
        """打印欢迎信息"""
        print("=" * 60)
        print("           PDF水印添加工具 v1.0")
        print("=" * 60)
        print("功能特性:")
        print("• 支持单个或批量PDF文件处理")
        print("• 自定义水印内容和样式")
        print("• 支持中文楷体字体")
        print("• 水印透明度、旋转角度可调")
        print("• 多行水印布局")
        print("• PDF转图片型PDF")
        print("• 自动检测中文字体显示")
        print("=" * 60)
    
    def check_environment(self):
        """检查运行环境"""
        print("正在检查运行环境...")
        
        # 检查字体
        kaiti_fonts = self.font_detector.find_kaiti_fonts()
        if kaiti_fonts:
            best_font = self.font_detector.get_best_kaiti_font()
            print(f"✓ 找到楷体字体: {best_font['name']}")
            
            # 更新配置中的字体路径
            self.config.font_path = best_font['path']
            self.processor = PDFProcessor(self.config)
        else:
            print("⚠ 未找到楷体字体，将使用默认字体")
            print("  建议安装楷体字体以获得最佳效果")
        
        print("环境检查完成\n")
    
    def show_main_menu(self) -> str:
        """显示主菜单"""
        print("\n" + "=" * 40)
        print("主菜单")
        print("=" * 40)
        print("1. 处理单个PDF文件")
        print("2. 处理多个PDF文件")
        print("3. 处理目录中的所有PDF文件")
        print("4. 查看水印配置")
        print("5. 修改水印配置")
        print("6. 检查字体状态")
        print("7. 帮助信息")
        print("0. 退出程序")
        print("=" * 40)
        
        return input("请选择操作 (0-7): ").strip()
    
    def process_single_file(self):
        """处理单个文件"""
        print("\n--- 处理单个PDF文件 ---")
        
        file_path = self.file_manager.select_single_file()
        if not file_path:
            return
        
        if not self.file_manager.validate_file_path(file_path):
            return
        
        print(f"开始处理文件: {file_path}")
        
        success = self.processor.process_pdf_file(file_path)
        
        if success:
            print("✓ 文件处理成功！")
        else:
            print("✗ 文件处理失败")
        
        input("按回车键继续...")
    
    def process_multiple_files(self):
        """处理多个文件"""
        print("\n--- 处理多个PDF文件 ---")
        
        file_paths = self.file_manager.select_multiple_files()
        if not file_paths:
            return
        
        valid_paths, invalid_paths = self.file_manager.validate_file_paths(file_paths)
        
        if not valid_paths:
            print("没有有效的PDF文件")
            input("按回车键继续...")
            return
        
        print(f"准备处理 {len(valid_paths)} 个文件")
        confirm = input("确认开始处理？(y/N): ").strip().lower()
        
        if confirm != 'y':
            print("操作已取消")
            return
        
        success_count, total_count = self.processor.process_multiple_files(valid_paths)
        
        print(f"\n处理完成: {success_count}/{total_count} 个文件成功")
        input("按回车键继续...")
    
    def process_directory(self):
        """处理目录中的文件"""
        print("\n--- 处理目录中的PDF文件 ---")
        
        directory = self.file_manager.select_directory()
        if not directory:
            return
        
        recursive = input("是否包含子目录？(y/N): ").strip().lower() == 'y'
        
        pdf_files = self.file_manager.find_pdf_files_in_directory(directory, recursive)
        
        if not pdf_files:
            print("目录中没有找到PDF文件")
            input("按回车键继续...")
            return
        
        print(f"找到 {len(pdf_files)} 个PDF文件")
        for i, file_path in enumerate(pdf_files[:5], 1):  # 显示前5个
            print(f"  {i}. {os.path.basename(file_path)}")
        
        if len(pdf_files) > 5:
            print(f"  ... 还有 {len(pdf_files) - 5} 个文件")
        
        confirm = input("确认开始处理？(y/N): ").strip().lower()
        
        if confirm != 'y':
            print("操作已取消")
            return
        
        success_count, total_count = self.processor.process_multiple_files(pdf_files)
        
        print(f"\n处理完成: {success_count}/{total_count} 个文件成功")
        input("按回车键继续...")
    
    def show_watermark_config(self):
        """显示水印配置"""
        print("\n--- 当前水印配置 ---")
        print(f"水印内容:")
        for line in self.config.get_text_lines():
            print(f"  {line}")
        print(f"字体名称: {self.config.font_name}")
        print(f"字体大小: {self.config.font_size}")
        print(f"旋转角度: {self.config.rotation}°")
        print(f"透明度: {self.config.opacity * 100}%")
        print(f"页面比例: {self.config.page_ratio * 100}%")
        print(f"每页行数: {self.config.rows_per_page}")
        print(f"垂直偏移: {self.config.vertical_center_offset}mm")
        print(f"水平对齐: {self.config.horizontal_align}")
        print(f"垂直对齐: {self.config.vertical_align}")
        
        input("按回车键继续...")
    
    def modify_watermark_config(self):
        """修改水印配置"""
        print("\n--- 修改水印配置 ---")
        print("当前配置已按要求设置，如需修改请联系开发者")
        print("默认配置:")
        print("• 内容: 小红书ID：11491392150 关注保研喵学姐 更多免费资料分享")
        print("• 字体: 楷体，54号")
        print("• 旋转: 45度")
        print("• 透明度: 20%")
        print("• 布局: 一页三行，垂直居中")
        
        input("按回车键继续...")
    
    def check_font_status(self):
        """检查字体状态"""
        print("\n--- 字体状态检查 ---")
        
        recommendations = self.font_detector.get_font_recommendations()
        for rec in recommendations:
            print(rec)
        
        # 测试字体渲染
        if self.config.font_path:
            print(f"\n正在测试字体渲染: {self.config.font_path}")
            if self.font_detector.test_font_rendering(self.config.font_path):
                print("✓ 字体渲染测试通过")
            else:
                print("✗ 字体渲染测试失败")
        
        input("按回车键继续...")
    
    def show_help(self):
        """显示帮助信息"""
        print("\n--- 帮助信息 ---")
        print("使用说明:")
        print("1. 选择要处理的PDF文件（单个、多个或整个目录）")
        print("2. 程序会自动添加水印并转换为图片型PDF")
        print("3. 输出文件保存在原文件相同目录中")
        print("")
        print("文件命名规则:")
        print("• 原文件: document.pdf")
        print("• 水印文件: document_watermarked.pdf")
        print("• 图片PDF: document_watermarked_image.pdf")
        print("")
        print("注意事项:")
        print("• 确保有足够的磁盘空间")
        print("• 处理大文件时请耐心等待")
        print("• 建议先用小文件测试效果")
        print("")
        print("技术支持:")
        print("• 支持的格式: PDF")
        print("• 输出格式: PDF（图片型）")
        print("• 字体支持: 楷体及其他中文字体")
        
        input("按回车键继续...")
