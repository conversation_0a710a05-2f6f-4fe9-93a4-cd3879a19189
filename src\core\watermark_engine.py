# -*- coding: utf-8 -*-
"""
水印引擎模块
负责生成和应用水印
"""

import io
import math
from typing import Tuple, List
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.colors import Color
from PyPDF2 import PdfReader, PdfWriter

from ..config.watermark_config import WatermarkConfig


class WatermarkEngine:
    """水印引擎类"""
    
    def __init__(self, config: WatermarkConfig):
        """
        初始化水印引擎
        
        Args:
            config: 水印配置对象
        """
        self.config = config
        self._font_registered = False
        self._setup_font()
    
    def _setup_font(self):
        """设置字体"""
        try:
            if self.config.font_path and self.config.font_path.endswith('.ttf'):
                # 注册TTF字体
                pdfmetrics.registerFont(TTFont(self.config.font_name, self.config.font_path))
                self._font_registered = True
                print(f"成功注册字体: {self.config.font_path}")
            else:
                print(f"使用系统默认字体: {self.config.font_name}")
        except Exception as e:
            print(f"字体设置失败: {e}")
            print("将使用默认字体")
    
    def create_watermark_pdf(self, page_width: float, page_height: float) -> bytes:
        """
        创建水印PDF
        
        Args:
            page_width: 页面宽度（点）
            page_height: 页面高度（点）
            
        Returns:
            水印PDF的字节数据
        """
        # 创建内存中的PDF
        packet = io.BytesIO()
        can = canvas.Canvas(packet, pagesize=(page_width, page_height))
        
        # 设置字体
        font_name = self.config.font_name if self._font_registered else "Helvetica"
        
        # 计算水印位置
        positions = self._calculate_watermark_positions(page_width, page_height)
        
        # 设置透明度
        alpha = self.config.opacity
        color = Color(
            self.config.color[0] / 255.0,
            self.config.color[1] / 255.0,
            self.config.color[2] / 255.0,
            alpha=alpha
        )
        
        # 绘制每个位置的水印
        for x, y in positions:
            self._draw_watermark_at_position(can, x, y, font_name, color)
        
        can.save()
        packet.seek(0)
        return packet.getvalue()
    
    def _calculate_watermark_positions(self, page_width: float, page_height: float) -> List[Tuple[float, float]]:
        """
        计算水印位置
        
        Args:
            page_width: 页面宽度
            page_height: 页面高度
            
        Returns:
            位置坐标列表 [(x, y), ...]
        """
        positions = []
        
        # 计算可用区域
        margin_h = self.config.get_margin_horizontal_points()
        margin_v = self.config.get_margin_vertical_points()

        available_width = page_width - 2 * margin_h
        available_height = page_height - 2 * margin_v

        # 根据页面比例调整水印大小区域
        watermark_width = available_width * self.config.page_ratio
        watermark_height = available_height * self.config.page_ratio

        # 计算行间距 - 增加间距使水印更分散
        row_height = watermark_height / self.config.rows_per_page
        col_width = watermark_width / 2  # 每行两列

        # 计算起始位置（考虑垂直居中偏移）
        start_x = margin_h + (available_width - watermark_width) / 2
        start_y = margin_v + (available_height - watermark_height) / 2
        start_y += self.config.get_vertical_offset_points()

        # 生成更分散的位置 - 交错排列
        for row in range(self.config.rows_per_page):
            y = start_y + row * row_height

            # 交错排列：奇数行偏左，偶数行偏右
            if row % 2 == 0:
                x = start_x + col_width * 0.3  # 左侧位置
            else:
                x = start_x + col_width * 1.7  # 右侧位置

            positions.append((x, y))
        
        return positions
    
    def _draw_watermark_at_position(self, canvas_obj, x: float, y: float, font_name: str, color):
        """
        在指定位置绘制水印
        
        Args:
            canvas_obj: Canvas对象
            x: X坐标
            y: Y坐标
            font_name: 字体名称
            color: 颜色对象
        """
        # 保存当前状态
        canvas_obj.saveState()
        
        # 移动到指定位置
        canvas_obj.translate(x, y)
        
        # 旋转
        canvas_obj.rotate(self.config.rotation)
        
        # 设置字体和颜色
        canvas_obj.setFont(font_name, self.config.font_size)
        canvas_obj.setFillColor(color)
        
        # 绘制多行文本
        text_lines = self.config.get_text_lines()
        line_height = self.config.font_size * self.config.line_spacing
        
        # 计算总文本高度用于垂直居中
        total_height = len(text_lines) * line_height
        start_y_offset = total_height / 2
        
        for i, line in enumerate(text_lines):
            line_y = start_y_offset - i * line_height
            
            # 水平对齐
            if self.config.horizontal_align == "center":
                text_width = canvas_obj.stringWidth(line, font_name, self.config.font_size)
                line_x = -text_width / 2
            elif self.config.horizontal_align == "left":
                line_x = 0
            else:  # right
                text_width = canvas_obj.stringWidth(line, font_name, self.config.font_size)
                line_x = -text_width
            
            canvas_obj.drawString(line_x, line_y, line)
        
        # 恢复状态
        canvas_obj.restoreState()
    
    def apply_watermark_to_page(self, page, page_width: float, page_height: float):
        """
        将水印应用到PDF页面
        
        Args:
            page: PDF页面对象
            page_width: 页面宽度
            page_height: 页面高度
            
        Returns:
            应用水印后的页面
        """
        # 创建水印PDF
        watermark_bytes = self.create_watermark_pdf(page_width, page_height)
        
        # 读取水印PDF
        watermark_pdf = PdfReader(io.BytesIO(watermark_bytes))
        watermark_page = watermark_pdf.pages[0]
        
        # 合并水印到原页面
        page.merge_page(watermark_page)
        
        return page
    
    def validate_font(self) -> bool:
        """验证字体是否可用"""
        try:
            # 创建测试PDF
            packet = io.BytesIO()
            can = canvas.Canvas(packet, pagesize=letter)
            
            font_name = self.config.font_name if self._font_registered else "Helvetica"
            can.setFont(font_name, self.config.font_size)
            
            # 测试绘制中文
            test_text = "测试中文字体显示"
            can.drawString(100, 100, test_text)
            can.save()
            
            return True
        except Exception as e:
            print(f"字体验证失败: {e}")
            return False
