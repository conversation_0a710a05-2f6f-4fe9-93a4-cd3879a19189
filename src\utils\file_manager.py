# -*- coding: utf-8 -*-
"""
文件管理模块
负责文件的导入、路径处理和输出管理
"""

import os
import glob
from typing import List, Optional, Tuple
from pathlib import Path
import tkinter as tk
from tkinter import filedialog


class FileManager:
    """文件管理器类"""
    
    def __init__(self):
        """初始化文件管理器"""
        self.supported_extensions = ['.pdf']
    
    def select_single_file(self, initial_dir: Optional[str] = None) -> Optional[str]:
        """
        选择单个PDF文件
        
        Args:
            initial_dir: 初始目录
            
        Returns:
            选择的文件路径，如果取消则返回None
        """
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 打开文件选择对话框
            file_path = filedialog.askopenfilename(
                title="选择PDF文件",
                initialdir=initial_dir or os.getcwd(),
                filetypes=[
                    ("PDF文件", "*.pdf"),
                    ("所有文件", "*.*")
                ]
            )
            
            root.destroy()
            
            if file_path:
                print(f"选择的文件: {file_path}")
                return file_path
            else:
                print("未选择文件")
                return None
                
        except Exception as e:
            print(f"选择文件时出错: {e}")
            return None
    
    def select_multiple_files(self, initial_dir: Optional[str] = None) -> List[str]:
        """
        选择多个PDF文件
        
        Args:
            initial_dir: 初始目录
            
        Returns:
            选择的文件路径列表
        """
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 打开多文件选择对话框
            file_paths = filedialog.askopenfilenames(
                title="选择PDF文件（可多选）",
                initialdir=initial_dir or os.getcwd(),
                filetypes=[
                    ("PDF文件", "*.pdf"),
                    ("所有文件", "*.*")
                ]
            )
            
            root.destroy()
            
            if file_paths:
                print(f"选择了 {len(file_paths)} 个文件")
                for path in file_paths:
                    print(f"  - {path}")
                return list(file_paths)
            else:
                print("未选择文件")
                return []
                
        except Exception as e:
            print(f"选择文件时出错: {e}")
            return []
    
    def select_directory(self, initial_dir: Optional[str] = None) -> Optional[str]:
        """
        选择目录
        
        Args:
            initial_dir: 初始目录
            
        Returns:
            选择的目录路径，如果取消则返回None
        """
        try:
            # 创建隐藏的根窗口
            root = tk.Tk()
            root.withdraw()
            
            # 打开目录选择对话框
            dir_path = filedialog.askdirectory(
                title="选择包含PDF文件的目录",
                initialdir=initial_dir or os.getcwd()
            )
            
            root.destroy()
            
            if dir_path:
                print(f"选择的目录: {dir_path}")
                return dir_path
            else:
                print("未选择目录")
                return None
                
        except Exception as e:
            print(f"选择目录时出错: {e}")
            return None
    
    def find_pdf_files_in_directory(self, directory: str, recursive: bool = False) -> List[str]:
        """
        在目录中查找PDF文件
        
        Args:
            directory: 目录路径
            recursive: 是否递归搜索子目录
            
        Returns:
            找到的PDF文件路径列表
        """
        pdf_files = []
        
        try:
            if not os.path.exists(directory):
                print(f"目录不存在: {directory}")
                return pdf_files
            
            if recursive:
                # 递归搜索
                pattern = os.path.join(directory, "**", "*.pdf")
                pdf_files = glob.glob(pattern, recursive=True)
            else:
                # 只搜索当前目录
                pattern = os.path.join(directory, "*.pdf")
                pdf_files = glob.glob(pattern)
            
            # 过滤和排序
            pdf_files = [f for f in pdf_files if os.path.isfile(f)]
            pdf_files.sort()
            
            print(f"在目录 {directory} 中找到 {len(pdf_files)} 个PDF文件")
            
            return pdf_files
            
        except Exception as e:
            print(f"搜索PDF文件时出错: {e}")
            return []
    
    def validate_file_path(self, file_path: str) -> bool:
        """
        验证文件路径
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否有效
        """
        if not file_path:
            return False
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        if not os.path.isfile(file_path):
            print(f"不是文件: {file_path}")
            return False
        
        if not file_path.lower().endswith('.pdf'):
            print(f"不是PDF文件: {file_path}")
            return False
        
        return True
    
    def validate_file_paths(self, file_paths: List[str]) -> Tuple[List[str], List[str]]:
        """
        验证多个文件路径
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            (有效路径列表, 无效路径列表)
        """
        valid_paths = []
        invalid_paths = []
        
        for path in file_paths:
            if self.validate_file_path(path):
                valid_paths.append(path)
            else:
                invalid_paths.append(path)
        
        if invalid_paths:
            print(f"发现 {len(invalid_paths)} 个无效文件:")
            for path in invalid_paths:
                print(f"  - {path}")
        
        print(f"有效文件: {len(valid_paths)} 个")
        return valid_paths, invalid_paths
    
    def get_file_info(self, file_path: str) -> dict:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return {}
            
            stat = os.stat(file_path)
            path_obj = Path(file_path)
            
            return {
                'path': file_path,
                'name': path_obj.name,
                'stem': path_obj.stem,
                'suffix': path_obj.suffix,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'modified_time': stat.st_mtime,
                'directory': str(path_obj.parent)
            }
            
        except Exception as e:
            print(f"获取文件信息时出错: {e}")
            return {}
    
    def create_output_directory(self, base_path: str, dir_name: str = "watermarked") -> str:
        """
        创建输出目录
        
        Args:
            base_path: 基础路径
            dir_name: 目录名称
            
        Returns:
            创建的目录路径
        """
        try:
            if os.path.isfile(base_path):
                base_path = os.path.dirname(base_path)
            
            output_dir = os.path.join(base_path, dir_name)
            
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建输出目录: {output_dir}")
            
            return output_dir
            
        except Exception as e:
            print(f"创建输出目录时出错: {e}")
            return base_path
    
    def get_available_space(self, path: str) -> int:
        """
        获取可用磁盘空间（字节）
        
        Args:
            path: 路径
            
        Returns:
            可用空间字节数
        """
        try:
            if os.path.isfile(path):
                path = os.path.dirname(path)
            
            statvfs = os.statvfs(path) if hasattr(os, 'statvfs') else None
            if statvfs:
                return statvfs.f_frsize * statvfs.f_bavail
            else:
                # Windows系统
                import shutil
                total, used, free = shutil.disk_usage(path)
                return free
                
        except Exception as e:
            print(f"获取磁盘空间时出错: {e}")
            return 0
