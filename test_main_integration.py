#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序集成
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.modern_gui import ModernWatermarkGUI
from src.config.watermark_config import WatermarkConfig

def test_integration():
    """测试集成功能"""
    print("🚀 测试主程序集成...")
    
    try:
        # 创建主程序实例
        app = ModernWatermarkGUI()
        
        print("✅ 主程序创建成功")
        print("📝 配置信息:")
        print(f"  - 文本: {app.config.text}")
        print(f"  - 字体: {app.config.font_name}")
        print(f"  - 大小: {app.config.font_size}")
        print(f"  - 透明度: {app.config.opacity}")
        print(f"  - 行数: {app.config.rows_per_page}")
        
        # 测试水印编辑器集成
        print("\n🔧 测试水印编辑器集成...")
        
        # 模拟打开水印编辑器（不实际运行GUI）
        from src.ui.watermark_editor_new import WatermarkEditor
        
        editor = WatermarkEditor(app.config)
        print("✅ 水印编辑器创建成功")
        
        # 测试配置更新
        original_text = app.config.text
        app.config.text = "测试水印文本"
        app.config.font_size = 30
        app.config.opacity = 0.3
        
        print(f"✅ 配置更新成功: {original_text} -> {app.config.text}")
        
        # 清理
        editor.root.destroy()
        app.root.destroy()
        
        print("✅ 集成测试完成")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_integration()
