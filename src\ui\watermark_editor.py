"""
水印编辑器 - 实时预览界面
提供左侧配置面板和右侧实时预览功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from PIL import Image, ImageDraw, ImageFont, ImageTk
import math
import os
import sys
from typing import Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config.watermark_config import WatermarkConfig
from src.utils.font_detector import FontDetector


class WatermarkEditor:
    """水印编辑器主类"""

    def __init__(self, config=None):
        self.root = tk.Tk()
        self.config = config if config else WatermarkConfig()
        self.font_detector = FontDetector()
        
        # 预览相关
        self.preview_width = 500  # 增大预览宽度
        self.preview_height = 700  # 增大预览高度
        self.preview_image = None
        self.preview_photo = None
        
        self.setup_window()
        self.create_widgets()
        self.bind_events()

        # 延迟更新预览，确保界面完全加载
        self.root.after(200, self.update_preview)
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("编辑水印")
        self.root.geometry("1200x800")  # 增大窗口尺寸
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左右分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # 左侧配置面板
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        self.create_config_panel(left_frame)

        # 右侧预览面板
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)  # 给预览更多空间
        self.create_preview_panel(right_frame)

        # 底部按钮
        self.create_bottom_buttons(main_frame)
    
    def create_config_panel(self, parent):
        """创建左侧配置面板"""
        # 使用滚动框架以防内容过多
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 配置面板内容
        left_frame = scrollable_frame
        
        # 来源部分
        source_frame = ttk.LabelFrame(left_frame, text="来源", padding=10)
        source_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文本选项
        self.text_var = tk.StringVar(value="文本(X)")
        ttk.Radiobutton(source_frame, text="文本(X):", variable=self.text_var, 
                       value="text").pack(anchor=tk.W)
        
        # 水印文本输入
        self.watermark_text = tk.Text(source_frame, height=3, width=40)
        self.watermark_text.pack(fill=tk.X, pady=5)
        self.watermark_text.insert("1.0", self.config.text)
        
        # 字体设置
        font_frame = ttk.Frame(source_frame)
        font_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(font_frame, text="字体(N):").pack(side=tk.LEFT)
        self.font_combo = ttk.Combobox(font_frame, width=15)
        self.font_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.load_fonts()
        
        ttk.Label(font_frame, text="字号(S):").pack(side=tk.LEFT)
        self.font_size_var = tk.StringVar(value=str(self.config.font_size))
        font_size_combo = ttk.Combobox(font_frame, textvariable=self.font_size_var, 
                                      width=8, values=[str(i) for i in range(8, 72, 2)])
        font_size_combo.pack(side=tk.LEFT, padx=5)
        
        # 外观部分
        appearance_frame = ttk.LabelFrame(left_frame, text="外观", padding=10)
        appearance_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 旋转角度
        rotation_frame = ttk.Frame(appearance_frame)
        rotation_frame.pack(fill=tk.X, pady=2)
        ttk.Label(rotation_frame, text="旋转:").pack(side=tk.LEFT)
        self.rotation_var = tk.StringVar(value="旋转45°")
        rotation_combo = ttk.Combobox(rotation_frame, textvariable=self.rotation_var,
                                     values=["旋转0°", "旋转15°", "旋转30°", "旋转45°", "旋转60°", "旋转90°"])
        rotation_combo.pack(side=tk.LEFT, padx=5)
        
        # 不透明度
        opacity_frame = ttk.Frame(appearance_frame)
        opacity_frame.pack(fill=tk.X, pady=2)
        ttk.Label(opacity_frame, text="不透明度(Y):").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=self.config.opacity * 100)
        opacity_scale = ttk.Scale(opacity_frame, from_=1, to=100, variable=self.opacity_var,
                                 orient=tk.HORIZONTAL, length=150)
        opacity_scale.pack(side=tk.LEFT, padx=5)
        self.opacity_label = ttk.Label(opacity_frame, text=f"{int(self.opacity_var.get())}%")
        self.opacity_label.pack(side=tk.LEFT, padx=5)
        
        # 相对页面比例
        ratio_frame = ttk.Frame(appearance_frame)
        ratio_frame.pack(fill=tk.X, pady=2)
        self.ratio_check = tk.BooleanVar(value=True)
        ttk.Checkbutton(ratio_frame, text="相对页面比例(L):", variable=self.ratio_check).pack(side=tk.LEFT)
        self.ratio_var = tk.StringVar(value="50%")
        ratio_combo = ttk.Combobox(ratio_frame, textvariable=self.ratio_var, width=8,
                                  values=["30%", "40%", "50%", "60%", "70%", "80%", "90%", "100%"])
        ratio_combo.pack(side=tk.LEFT, padx=5)
        
        # 多行水印
        multiline_frame = ttk.Frame(appearance_frame)
        multiline_frame.pack(fill=tk.X, pady=2)
        self.multiline_check = tk.BooleanVar(value=True)
        ttk.Checkbutton(multiline_frame, text="多行水印(H):", variable=self.multiline_check).pack(side=tk.LEFT)
        self.rows_var = tk.StringVar(value="一页三行")
        rows_combo = ttk.Combobox(multiline_frame, textvariable=self.rows_var, width=10,
                                 values=["一页一行", "一页二行", "一页三行", "一页四行", "一页五行"])
        rows_combo.pack(side=tk.LEFT, padx=5)
        
        # 位置部分
        position_frame = ttk.LabelFrame(left_frame, text="位置", padding=10)
        position_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 垂直对齐
        v_align_frame = ttk.Frame(position_frame)
        v_align_frame.pack(fill=tk.X, pady=2)
        ttk.Label(v_align_frame, text="垂直对齐(T):").pack(side=tk.LEFT)
        self.v_align_var = tk.StringVar(value="居中")
        v_align_combo = ttk.Combobox(v_align_frame, textvariable=self.v_align_var, width=8,
                                    values=["居上", "居中", "居下"])
        v_align_combo.pack(side=tk.LEFT, padx=5)
        
        # 水平对齐
        h_align_frame = ttk.Frame(position_frame)
        h_align_frame.pack(fill=tk.X, pady=2)
        ttk.Label(h_align_frame, text="水平对齐(Z):").pack(side=tk.LEFT)
        self.h_align_var = tk.StringVar(value="居中")
        h_align_combo = ttk.Combobox(h_align_frame, textvariable=self.h_align_var, width=8,
                                    values=["居左", "居中", "居右"])
        h_align_combo.pack(side=tk.LEFT, padx=5)
        
        # 应用于
        apply_frame = ttk.Frame(position_frame)
        apply_frame.pack(fill=tk.X, pady=2)
        ttk.Label(apply_frame, text="应用于(A):").pack(side=tk.LEFT)
        self.apply_var = tk.StringVar(value="全部页面")
        apply_combo = ttk.Combobox(apply_frame, textvariable=self.apply_var, width=12,
                                  values=["全部页面", "奇数页面", "偶数页面", "指定页面"])
        apply_combo.pack(side=tk.LEFT, padx=5)
    
    def create_preview_panel(self, parent):
        """创建右侧预览面板"""
        # 预览标题
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(title_frame, text="预览:", font=("微软雅黑", 14, "bold")).pack(side=tk.LEFT)

        # 预览画布容器
        preview_container = ttk.Frame(parent, relief=tk.SUNKEN, borderwidth=2)
        preview_container.pack(fill=tk.BOTH, expand=True)

        # 预览画布 - 居中显示
        self.preview_canvas = tk.Canvas(preview_container, bg="white")
        self.preview_canvas.pack(fill=tk.BOTH, expand=True)

        # 简化的页面信息（只显示单页）
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        ttk.Label(info_frame, text="第 1 页 (预览)", font=("微软雅黑", 10)).pack()
    
    def create_bottom_buttons(self, parent):
        """创建底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0), side=tk.BOTTOM)

        # 右对齐按钮
        ttk.Button(button_frame, text="取消", command=self.cancel, width=10).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="保存", command=self.save_config, width=10).pack(side=tk.RIGHT, padx=(0, 5))
    
    def load_fonts(self):
        """加载系统字体"""
        try:
            # 使用固定的字体列表，因为字体检测器方法名不匹配
            font_names = ["楷体", "宋体", "黑体", "仿宋", "微软雅黑", "Arial", "Times New Roman"]
            self.font_combo['values'] = font_names

            # 设置默认字体
            if self.config.font_name in font_names:
                self.font_combo.set(self.config.font_name)
            else:
                self.font_combo.set("楷体")
        except Exception as e:
            print(f"加载字体失败: {e}")
            self.font_combo['values'] = ["宋体", "黑体", "楷体", "仿宋"]
            self.font_combo.set("楷体")
    
    def bind_events(self):
        """绑定事件"""
        # 绑定所有控件的变化事件到预览更新
        self.watermark_text.bind('<KeyRelease>', lambda e: self.update_preview())
        self.font_combo.bind('<<ComboboxSelected>>', lambda e: self.update_preview())
        self.font_size_var.trace('w', lambda *args: self.update_preview())
        self.rotation_var.trace('w', lambda *args: self.update_preview())
        self.opacity_var.trace('w', lambda *args: self.update_opacity_label())
        self.ratio_var.trace('w', lambda *args: self.update_preview())
        self.rows_var.trace('w', lambda *args: self.update_preview())
        self.v_align_var.trace('w', lambda *args: self.update_preview())
        self.h_align_var.trace('w', lambda *args: self.update_preview())
    
    def update_opacity_label(self):
        """更新透明度标签"""
        self.opacity_label.config(text=f"{int(self.opacity_var.get())}%")
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        try:
            # 获取画布实际大小
            self.preview_canvas.update()
            canvas_width = self.preview_canvas.winfo_width()
            canvas_height = self.preview_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                # 画布还没有正确初始化，稍后再试
                self.root.after(100, self.update_preview)
                return

            # 创建预览图像 - 使用A4比例
            img_width = min(canvas_width - 40, 400)
            img_height = int(img_width * 1.414)  # A4比例

            if img_height > canvas_height - 40:
                img_height = canvas_height - 40
                img_width = int(img_height / 1.414)

            img = Image.new('RGB', (img_width, img_height), 'white')

            # 获取当前配置
            text = self.watermark_text.get("1.0", tk.END).strip()
            if not text:
                text = "水印预览"

            font_size = int(self.font_size_var.get() or 24)
            opacity = int(self.opacity_var.get())
            rotation = self.get_rotation_angle()
            rows = self.get_rows_count()

            # 尝试加载字体
            font = self.load_preview_font(font_size)

            # 计算水印位置
            positions = self.calculate_watermark_positions(rows, img_width, img_height)

            # 绘制水印
            for x, y in positions:
                self.draw_watermark_at_position(img, text, font, x, y, opacity, rotation)

            # 转换为PhotoImage并显示
            self.preview_photo = ImageTk.PhotoImage(img)
            self.preview_canvas.delete("all")

            # 居中显示图像
            canvas_center_x = canvas_width // 2
            canvas_center_y = canvas_height // 2
            self.preview_canvas.create_image(canvas_center_x, canvas_center_y, image=self.preview_photo)

            # 添加边框
            border_x1 = canvas_center_x - img_width // 2
            border_y1 = canvas_center_y - img_height // 2
            border_x2 = canvas_center_x + img_width // 2
            border_y2 = canvas_center_y + img_height // 2
            self.preview_canvas.create_rectangle(border_x1, border_y1, border_x2, border_y2,
                                               outline="gray", width=1)

        except Exception as e:
            print(f"预览更新失败: {e}")
            # 显示错误信息在画布上
            self.preview_canvas.delete("all")
            self.preview_canvas.create_text(canvas_width//2 if canvas_width > 1 else 200,
                                          canvas_height//2 if canvas_height > 1 else 200,
                                          text=f"预览失败: {str(e)}", fill="red")

    def load_preview_font(self, font_size):
        """加载预览字体"""
        font_paths = [
            "C:/Windows/Fonts/STKAITI.TTF",
            "C:/Windows/Fonts/simkai.ttf",
            "C:/Windows/Fonts/kaiti.ttf",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/simsun.ttc"
        ]

        for font_path in font_paths:
            try:
                return ImageFont.truetype(font_path, font_size)
            except:
                continue

        return ImageFont.load_default()

    def draw_watermark_at_position(self, img, text, font, x, y, opacity, rotation):
        """在指定位置绘制水印"""
        lines = text.split('\n')
        line_height = font.size + 5

        # 计算文本总高度
        total_height = len(lines) * line_height

        # 创建足够大的临时图像
        temp_size = max(300, int(total_height * 1.5))
        temp_img = Image.new('RGBA', (temp_size, temp_size), (255, 255, 255, 0))
        temp_draw = ImageDraw.Draw(temp_img)

        # 绘制每行文本
        for i, line in enumerate(lines):
            if line.strip():
                color = (128, 128, 128, int(255 * opacity / 100))
                temp_draw.text((20, 20 + i * line_height), line, font=font, fill=color)

        # 旋转
        if rotation != 0:
            temp_img = temp_img.rotate(rotation, expand=True)

        # 粘贴到主图像
        try:
            img.paste(temp_img, (int(x), int(y)), temp_img)
        except:
            pass  # 忽略超出边界的情况
    
    def get_rotation_angle(self) -> float:
        """获取旋转角度"""
        rotation_text = self.rotation_var.get()
        if "0°" in rotation_text:
            return 0
        elif "15°" in rotation_text:
            return 15
        elif "30°" in rotation_text:
            return 30
        elif "45°" in rotation_text:
            return 45
        elif "60°" in rotation_text:
            return 60
        elif "90°" in rotation_text:
            return 90
        return 45
    
    def get_rows_count(self) -> int:
        """获取行数"""
        rows_text = self.rows_var.get()
        if "一行" in rows_text:
            return 1
        elif "二行" in rows_text:
            return 2
        elif "三行" in rows_text:
            return 3
        elif "四行" in rows_text:
            return 4
        elif "五行" in rows_text:
            return 5
        return 3
    
    def calculate_watermark_positions(self, rows: int, img_width: int, img_height: int) -> list:
        """计算水印位置"""
        positions = []

        # 计算可用区域
        margin = 40
        available_width = img_width - 2 * margin
        available_height = img_height - 2 * margin

        # 计算行间距
        row_height = available_height / rows if rows > 0 else available_height

        # 生成位置
        for row in range(rows):
            y = margin + row * row_height + row_height / 3

            # 交错排列
            if row % 2 == 0:
                x = margin + available_width * 0.2
            else:
                x = margin + available_width * 0.6

            positions.append((x, y))

        return positions
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.config.text = self.watermark_text.get("1.0", tk.END).strip()
            self.config.font_name = self.font_combo.get()
            self.config.font_size = int(self.font_size_var.get() or 24)
            self.config.rotation = self.get_rotation_angle()
            self.config.opacity = self.opacity_var.get() / 100
            self.config.rows_per_page = self.get_rows_count()

            # 更新页面比例
            ratio_text = self.ratio_var.get()
            ratio_value = int(ratio_text.replace('%', '')) / 100
            self.config.page_ratio = ratio_value

            # 重新初始化配置（更新text_lines等）
            self.config.__post_init__()

            messagebox.showinfo("成功", "水印配置已保存！")
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def cancel(self):
        """取消"""
        self.root.destroy()
    
    def run(self):
        """运行界面"""
        self.root.mainloop()


def main():
    """主函数"""
    app = WatermarkEditor()
    app.run()


if __name__ == "__main__":
    main()
