"""
水印编辑器 - 实时预览界面
提供左侧配置面板和右侧实时预览功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from PIL import Image, ImageDraw, ImageFont, ImageTk
import math
import os
import sys
from typing import Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config.watermark_config import WatermarkConfig
from src.utils.font_detector import FontDetector


class WatermarkEditor:
    """水印编辑器主类"""

    def __init__(self, config=None):
        self.root = tk.Tk()
        self.config = config if config else WatermarkConfig()
        self.font_detector = FontDetector()
        
        # 预览相关
        self.preview_width = 400
        self.preview_height = 600
        self.preview_image = None
        self.preview_photo = None
        
        self.setup_window()
        self.create_widgets()
        self.bind_events()
        self.update_preview()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("编辑水印")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置窗口图标和样式
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧配置面板
        self.create_config_panel(main_frame)
        
        # 右侧预览面板
        self.create_preview_panel(main_frame)
        
        # 底部按钮
        self.create_bottom_buttons(main_frame)
    
    def create_config_panel(self, parent):
        """创建左侧配置面板"""
        # 左侧框架
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=False, padx=(0, 10))
        left_frame.configure(width=400)
        
        # 来源部分
        source_frame = ttk.LabelFrame(left_frame, text="来源", padding=10)
        source_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文本选项
        self.text_var = tk.StringVar(value="文本(X)")
        ttk.Radiobutton(source_frame, text="文本(X):", variable=self.text_var, 
                       value="text").pack(anchor=tk.W)
        
        # 水印文本输入
        self.watermark_text = tk.Text(source_frame, height=3, width=40)
        self.watermark_text.pack(fill=tk.X, pady=5)
        self.watermark_text.insert("1.0", self.config.watermark_text)
        
        # 字体设置
        font_frame = ttk.Frame(source_frame)
        font_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(font_frame, text="字体(N):").pack(side=tk.LEFT)
        self.font_combo = ttk.Combobox(font_frame, width=15)
        self.font_combo.pack(side=tk.LEFT, padx=(5, 10))
        self.load_fonts()
        
        ttk.Label(font_frame, text="字号(S):").pack(side=tk.LEFT)
        self.font_size_var = tk.StringVar(value=str(self.config.font_size))
        font_size_combo = ttk.Combobox(font_frame, textvariable=self.font_size_var, 
                                      width=8, values=[str(i) for i in range(8, 72, 2)])
        font_size_combo.pack(side=tk.LEFT, padx=5)
        
        # 外观部分
        appearance_frame = ttk.LabelFrame(left_frame, text="外观", padding=10)
        appearance_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 旋转角度
        rotation_frame = ttk.Frame(appearance_frame)
        rotation_frame.pack(fill=tk.X, pady=2)
        ttk.Label(rotation_frame, text="旋转:").pack(side=tk.LEFT)
        self.rotation_var = tk.StringVar(value="旋转45°")
        rotation_combo = ttk.Combobox(rotation_frame, textvariable=self.rotation_var,
                                     values=["旋转0°", "旋转15°", "旋转30°", "旋转45°", "旋转60°", "旋转90°"])
        rotation_combo.pack(side=tk.LEFT, padx=5)
        
        # 不透明度
        opacity_frame = ttk.Frame(appearance_frame)
        opacity_frame.pack(fill=tk.X, pady=2)
        ttk.Label(opacity_frame, text="不透明度(Y):").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=self.config.opacity * 100)
        opacity_scale = ttk.Scale(opacity_frame, from_=1, to=100, variable=self.opacity_var,
                                 orient=tk.HORIZONTAL, length=150)
        opacity_scale.pack(side=tk.LEFT, padx=5)
        self.opacity_label = ttk.Label(opacity_frame, text=f"{int(self.opacity_var.get())}%")
        self.opacity_label.pack(side=tk.LEFT, padx=5)
        
        # 相对页面比例
        ratio_frame = ttk.Frame(appearance_frame)
        ratio_frame.pack(fill=tk.X, pady=2)
        self.ratio_check = tk.BooleanVar(value=True)
        ttk.Checkbutton(ratio_frame, text="相对页面比例(L):", variable=self.ratio_check).pack(side=tk.LEFT)
        self.ratio_var = tk.StringVar(value="50%")
        ratio_combo = ttk.Combobox(ratio_frame, textvariable=self.ratio_var, width=8,
                                  values=["30%", "40%", "50%", "60%", "70%", "80%", "90%", "100%"])
        ratio_combo.pack(side=tk.LEFT, padx=5)
        
        # 多行水印
        multiline_frame = ttk.Frame(appearance_frame)
        multiline_frame.pack(fill=tk.X, pady=2)
        self.multiline_check = tk.BooleanVar(value=True)
        ttk.Checkbutton(multiline_frame, text="多行水印(H):", variable=self.multiline_check).pack(side=tk.LEFT)
        self.rows_var = tk.StringVar(value="一页三行")
        rows_combo = ttk.Combobox(multiline_frame, textvariable=self.rows_var, width=10,
                                 values=["一页一行", "一页二行", "一页三行", "一页四行", "一页五行"])
        rows_combo.pack(side=tk.LEFT, padx=5)
        
        # 位置部分
        position_frame = ttk.LabelFrame(left_frame, text="位置", padding=10)
        position_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 垂直对齐
        v_align_frame = ttk.Frame(position_frame)
        v_align_frame.pack(fill=tk.X, pady=2)
        ttk.Label(v_align_frame, text="垂直对齐(T):").pack(side=tk.LEFT)
        self.v_align_var = tk.StringVar(value="居中")
        v_align_combo = ttk.Combobox(v_align_frame, textvariable=self.v_align_var, width=8,
                                    values=["居上", "居中", "居下"])
        v_align_combo.pack(side=tk.LEFT, padx=5)
        
        # 水平对齐
        h_align_frame = ttk.Frame(position_frame)
        h_align_frame.pack(fill=tk.X, pady=2)
        ttk.Label(h_align_frame, text="水平对齐(Z):").pack(side=tk.LEFT)
        self.h_align_var = tk.StringVar(value="居中")
        h_align_combo = ttk.Combobox(h_align_frame, textvariable=self.h_align_var, width=8,
                                    values=["居左", "居中", "居右"])
        h_align_combo.pack(side=tk.LEFT, padx=5)
        
        # 应用于
        apply_frame = ttk.Frame(position_frame)
        apply_frame.pack(fill=tk.X, pady=2)
        ttk.Label(apply_frame, text="应用于(A):").pack(side=tk.LEFT)
        self.apply_var = tk.StringVar(value="全部页面")
        apply_combo = ttk.Combobox(apply_frame, textvariable=self.apply_var, width=12,
                                  values=["全部页面", "奇数页面", "偶数页面", "指定页面"])
        apply_combo.pack(side=tk.LEFT, padx=5)
    
    def create_preview_panel(self, parent):
        """创建右侧预览面板"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 预览标题
        ttk.Label(right_frame, text="预览:", font=("微软雅黑", 12)).pack(anchor=tk.W, pady=(0, 10))
        
        # 预览画布容器
        preview_container = ttk.Frame(right_frame, relief=tk.SUNKEN, borderwidth=2)
        preview_container.pack(fill=tk.BOTH, expand=True)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_container, bg="white", 
                                       width=self.preview_width, height=self.preview_height)
        self.preview_canvas.pack(expand=True)
        
        # 页面导航
        nav_frame = ttk.Frame(right_frame)
        nav_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(nav_frame, text="<<", width=3).pack(side=tk.LEFT)
        ttk.Button(nav_frame, text="<", width=3).pack(side=tk.LEFT, padx=5)
        
        page_frame = ttk.Frame(nav_frame)
        page_frame.pack(side=tk.LEFT, padx=20)
        ttk.Entry(page_frame, width=5, justify=tk.CENTER).pack(side=tk.LEFT)
        ttk.Label(page_frame, text=" / 31").pack(side=tk.LEFT)
        
        ttk.Button(nav_frame, text=">", width=3).pack(side=tk.RIGHT)
        ttk.Button(nav_frame, text=">>", width=3).pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_bottom_buttons(self, parent):
        """创建底部按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=10)
        
        # 右对齐按钮
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="保存", command=self.save_config, 
                  style="Accent.TButton").pack(side=tk.RIGHT)
    
    def load_fonts(self):
        """加载系统字体"""
        try:
            fonts = self.font_detector.get_available_fonts()
            font_names = [font['name'] for font in fonts]
            self.font_combo['values'] = font_names
            
            # 设置默认字体
            if self.config.font_name in font_names:
                self.font_combo.set(self.config.font_name)
            elif font_names:
                self.font_combo.set(font_names[0])
        except Exception as e:
            print(f"加载字体失败: {e}")
            self.font_combo['values'] = ["宋体", "黑体", "楷体", "仿宋"]
            self.font_combo.set("楷体")
    
    def bind_events(self):
        """绑定事件"""
        # 绑定所有控件的变化事件到预览更新
        self.watermark_text.bind('<KeyRelease>', lambda e: self.update_preview())
        self.font_combo.bind('<<ComboboxSelected>>', lambda e: self.update_preview())
        self.font_size_var.trace('w', lambda *args: self.update_preview())
        self.rotation_var.trace('w', lambda *args: self.update_preview())
        self.opacity_var.trace('w', lambda *args: self.update_opacity_label())
        self.ratio_var.trace('w', lambda *args: self.update_preview())
        self.rows_var.trace('w', lambda *args: self.update_preview())
        self.v_align_var.trace('w', lambda *args: self.update_preview())
        self.h_align_var.trace('w', lambda *args: self.update_preview())
    
    def update_opacity_label(self):
        """更新透明度标签"""
        self.opacity_label.config(text=f"{int(self.opacity_var.get())}%")
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        try:
            # 创建预览图像
            img = Image.new('RGB', (self.preview_width, self.preview_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 获取当前配置
            text = self.watermark_text.get("1.0", tk.END).strip()
            if not text:
                text = "水印预览"
            
            font_size = int(self.font_size_var.get() or 24)
            opacity = int(self.opacity_var.get())
            rotation = self.get_rotation_angle()
            rows = self.get_rows_count()
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/simkai.ttf", font_size)
            except:
                font = ImageFont.load_default()
            
            # 计算水印位置
            positions = self.calculate_watermark_positions(rows)
            
            # 绘制水印
            for x, y in positions:
                # 创建临时图像用于旋转
                temp_img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))
                temp_draw = ImageDraw.Draw(temp_img)
                
                # 绘制文本
                color = (128, 128, 128, int(255 * opacity / 100))
                temp_draw.text((10, 10), text, font=font, fill=color)
                
                # 旋转
                if rotation != 0:
                    temp_img = temp_img.rotate(rotation, expand=True)
                
                # 粘贴到主图像
                img.paste(temp_img, (int(x), int(y)), temp_img)
            
            # 转换为PhotoImage并显示
            self.preview_photo = ImageTk.PhotoImage(img)
            self.preview_canvas.delete("all")
            self.preview_canvas.create_image(self.preview_width//2, self.preview_height//2, 
                                           image=self.preview_photo)
            
        except Exception as e:
            print(f"预览更新失败: {e}")
    
    def get_rotation_angle(self) -> float:
        """获取旋转角度"""
        rotation_text = self.rotation_var.get()
        if "0°" in rotation_text:
            return 0
        elif "15°" in rotation_text:
            return 15
        elif "30°" in rotation_text:
            return 30
        elif "45°" in rotation_text:
            return 45
        elif "60°" in rotation_text:
            return 60
        elif "90°" in rotation_text:
            return 90
        return 45
    
    def get_rows_count(self) -> int:
        """获取行数"""
        rows_text = self.rows_var.get()
        if "一行" in rows_text:
            return 1
        elif "二行" in rows_text:
            return 2
        elif "三行" in rows_text:
            return 3
        elif "四行" in rows_text:
            return 4
        elif "五行" in rows_text:
            return 5
        return 3
    
    def calculate_watermark_positions(self, rows: int) -> list:
        """计算水印位置"""
        positions = []
        
        # 计算可用区域
        margin = 50
        available_width = self.preview_width - 2 * margin
        available_height = self.preview_height - 2 * margin
        
        # 计算行间距
        row_height = available_height / rows
        
        # 生成位置
        for row in range(rows):
            y = margin + row * row_height + row_height / 2
            
            # 交错排列
            if row % 2 == 0:
                x = margin + available_width * 0.25
            else:
                x = margin + available_width * 0.75
            
            positions.append((x, y))
        
        return positions
    
    def save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.config.watermark_text = self.watermark_text.get("1.0", tk.END).strip()
            self.config.font_name = self.font_combo.get()
            self.config.font_size = int(self.font_size_var.get() or 24)
            self.config.rotation = self.get_rotation_angle()
            self.config.opacity = self.opacity_var.get() / 100
            self.config.rows_per_page = self.get_rows_count()

            # 更新页面比例
            ratio_text = self.ratio_var.get()
            ratio_value = int(ratio_text.replace('%', '')) / 100
            self.config.page_ratio = ratio_value

            messagebox.showinfo("成功", "水印配置已保存！")
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def cancel(self):
        """取消"""
        self.root.destroy()
    
    def run(self):
        """运行界面"""
        self.root.mainloop()


def main():
    """主函数"""
    app = WatermarkEditor()
    app.run()


if __name__ == "__main__":
    main()
