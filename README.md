# PDF水印添加工具

一个功能完整的PDF水印添加工具，支持批量处理和中文字体。

## 功能特性

- 支持单个或批量PDF文件处理
- 自定义水印内容和样式
- 支持中文楷体字体
- 水印透明度、旋转角度可调
- 多行水印布局
- PDF转图片型PDF
- 自动检测中文字体显示

## 项目结构

```
pdf_watermark_tool/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── config/            # 配置模块
│   │   ├── __init__.py
│   │   └── watermark_config.py
│   ├── core/              # 核心处理模块
│   │   ├── __init__.py
│   │   ├── pdf_processor.py
│   │   └── watermark_engine.py
│   ├── utils/             # 工具模块
│   │   ├── __init__.py
│   │   ├── file_manager.py
│   │   └── font_detector.py
│   └── ui/                # 用户界面
│       ├── __init__.py
│       └── cli.py
├── fonts/                 # 字体文件目录
├── tests/                 # 测试目录
├── examples/              # 示例文件
├── requirements.txt       # 依赖包
├── main.py               # 主程序入口
└── README.md             # 项目说明
```

## 安装和使用

### 快速安装

1. **自动安装（推荐）**：
```bash
python install.py
```

2. **手动安装**：
```bash
pip install -r requirements.txt
```

### 运行程序

1. **启动现代化GUI界面（推荐）**：
```bash
python main.py
```
直接启动美观易用的图形界面

2. **备用启动方式**：
```bash
# 旧版GUI界面
python gui_main.py

# 命令行界面
python cli_main.py
```

3. **运行演示**：
```bash
# 现代化GUI演示
python examples/gui_demo.py

# 功能演示
python examples/demo.py
```

4. **运行测试**：
```bash
python tests/test_watermark.py
```

### 使用步骤

#### 🖥️ 现代化GUI界面（推荐）
1. **启动程序**：运行 `python main.py` 启动现代化GUI
2. **添加文件**：
   - 点击"📄 添加文件"选择单个或多个PDF
   - 点击"📁 添加目录"选择整个目录的PDF文件
   - 支持在文件列表中删除不需要的文件
3. **配置水印**：
   - 查看当前配置（默认已优化）
   - 可选：点击"修改配置"进入配置对话框
   - 支持标签页式的详细配置
4. **开始处理**：
   - 点击"🎯 开始处理"按钮
   - 实时查看处理进度和详细日志
   - 可随时停止处理
5. **查看结果**：
   - 处理完成后自动显示结果
   - 点击"📁 打开输出目录"查看生成的文件

#### 💻 命令行界面（备用）
1. 运行 `python cli_main.py` 启动命令行版本
2. 按菜单选择处理方式
3. 程序自动处理并保存结果

### 输出文件说明

- 原文件：`document.pdf`
- 水印文件：`document_watermarked.pdf`
- 图片PDF：`document_watermarked_image.pdf`

## 水印配置

- 内容：小红书ID：11491392150 关注保研喵学姐 更多免费资料分享
- 字体：楷体
- 字号：54
- 旋转角度：45度
- 透明度：20%
- 页面比例：50%
- 布局：一页三行
- 位置：垂直居中11毫米，水平对齐垂直居中
