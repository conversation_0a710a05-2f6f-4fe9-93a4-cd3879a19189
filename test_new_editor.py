#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的水印编辑器
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.watermark_editor_new import WatermarkEditor
from src.config.watermark_config import WatermarkConfig

def main():
    """主函数"""
    print("🚀 启动新水印编辑器测试...")
    
    try:
        # 创建配置
        config = WatermarkConfig()
        
        # 创建编辑器
        editor = WatermarkEditor(config)
        
        # 运行编辑器
        editor.run()
        
        print("✅ 新水印编辑器测试完成")
        print(f"📝 最终配置: {config.text}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
