# PDF水印添加工具 - GUI使用说明

## 🎉 GUI界面已添加！

现在您的PDF水印添加工具拥有了美观易用的图形用户界面！

## 🚀 启动GUI界面

### 方式1：主程序选择（推荐）
```bash
python main.py
```
然后选择 "1. GUI图形界面"

### 方式2：直接启动GUI
```bash
python gui_main.py
```

### 方式3：命令行参数
```bash
python main.py --gui
```

## 🖥️ GUI界面功能

### 1. 文件选择区域
- **选择单个文件**：选择一个PDF文件进行处理
- **选择多个文件**：同时选择多个PDF文件
- **选择目录**：选择整个目录中的所有PDF文件
- **清空列表**：清除当前选择的文件
- **文件列表**：显示已选择的文件名称

### 2. 水印配置区域
- **水印内容显示**：显示完整的水印文本
- **配置参数**：显示字体、大小、角度等设置
- **字体状态**：实时显示字体检测结果

### 3. 处理控制区域
- **开始处理**：启动批量处理
- **停止处理**：中途停止处理
- **进度条**：显示处理进度
- **进度标签**：显示当前处理状态

### 4. 日志输出区域
- **处理日志**：详细的处理过程记录
- **错误信息**：显示处理中的错误
- **清空日志**：清除日志内容
- **自动滚动**：自动滚动到最新日志

### 5. 状态栏
- **状态信息**：显示当前程序状态
- **版本信息**：显示程序版本

## 📋 使用步骤

### 步骤1：启动程序
```bash
python main.py
```
选择 "1. GUI图形界面"

### 步骤2：选择文件
1. 点击"选择单个文件"选择一个PDF
2. 或点击"选择多个文件"选择多个PDF
3. 或点击"选择目录"选择整个目录

### 步骤3：确认配置
查看"水印配置"区域，确认设置正确：
- 水印内容：小红书ID：11491392150 关注保研喵学姐 更多免费资料分享
- 字体：楷体，54号
- 其他参数：45度旋转，20%透明度等

### 步骤4：开始处理
1. 点击"开始处理"按钮
2. 确认处理对话框
3. 观察进度条和日志输出

### 步骤5：查看结果
处理完成后：
1. 查看完成对话框
2. 检查原文件目录中的输出文件
3. 验证水印效果

## ✨ GUI界面特色功能

### 🔄 多线程处理
- 界面不会卡顿
- 可以随时停止处理
- 实时显示处理进度

### 📊 实时反馈
- 进度条显示处理进度
- 详细的日志输出
- 错误信息及时显示

### 🎯 用户友好
- 直观的按钮和标签
- 清晰的状态提示
- 简单的操作流程

### 🛡️ 错误处理
- 文件验证
- 异常捕获
- 友好的错误提示

## 🎮 GUI演示

运行GUI演示程序：
```bash
python examples/gui_demo.py
```

这会：
1. 创建演示PDF文件
2. 显示GUI功能介绍
3. 启动GUI界面
4. 引导您体验各项功能

## 📸 界面预览

GUI界面包含以下区域：
```
┌─────────────────────────────────────────────────────────┐
│                PDF水印添加工具 v1.0                      │
├─────────────────────────────────────────────────────────┤
│ 文件选择                                                │
│ [选择单个文件] [选择多个文件] [选择目录] [清空列表]        │
│ 选中的文件:                                             │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 文件列表                                            │ │
│ └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 水印配置                                                │
│ 水印内容: [文本显示区域]                                │
│ 字体: 楷体 | 字号: 54 | 旋转: 45° | 透明度: 20%         │
│ ✓ 字体就绪: STKAITI                                     │
├─────────────────────────────────────────────────────────┤
│ 处理控制                                                │
│ [开始处理] [停止处理]                                   │
│ ████████████████████████████████████████████ 100%      │
│ 处理完成: 3/3 个文件成功                                │
├─────────────────────────────────────────────────────────┤
│ 处理日志                                                │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ 详细的处理日志输出...                               │ │
│ └─────────────────────────────────────────────────────┘ │
│                                          [清空日志]     │
├─────────────────────────────────────────────────────────┤
│ 就绪                                              v1.0  │
└─────────────────────────────────────────────────────────┘
```

## 🔧 技术特性

### 界面框架
- **tkinter**：Python标准GUI库
- **ttk**：现代化的界面组件
- **threading**：多线程处理

### 核心功能
- **文件对话框**：系统原生文件选择
- **进度显示**：实时进度条和百分比
- **日志系统**：滚动文本框显示详细信息
- **状态管理**：按钮状态和界面状态管理

### 用户体验
- **响应式设计**：窗口可调整大小
- **键盘支持**：支持常用快捷键
- **错误处理**：友好的错误对话框
- **确认对话框**：重要操作前确认

## 🎊 总结

现在您的PDF水印添加工具拥有了：

✅ **双界面支持**
- GUI图形界面（推荐日常使用）
- CLI命令行界面（适合批处理）

✅ **完整功能**
- 所有核心功能在GUI中都可使用
- 直观的操作流程
- 实时的处理反馈

✅ **用户友好**
- 无需记忆命令
- 可视化的文件选择
- 清晰的状态显示

立即体验GUI界面：
```bash
python main.py
```
选择 "1. GUI图形界面" 开始使用！🚀
