#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI界面演示脚本
展示GUI界面的功能和特性
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.ui.gui import WatermarkGUI
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter


def create_demo_files():
    """创建演示用的PDF文件"""
    demo_dir = os.path.dirname(__file__)
    
    # 创建几个不同类型的演示PDF
    demo_files = [
        ("简单文档.pdf", "这是一个简单的PDF文档\n用于测试GUI界面的水印功能"),
        ("多页文档.pdf", "这是一个多页PDF文档\n第一页内容"),
        ("测试报告.pdf", "PDF水印工具测试报告\n功能验证文档")
    ]
    
    created_files = []
    
    for filename, content in demo_files:
        file_path = os.path.join(demo_dir, filename)
        
        try:
            c = canvas.Canvas(file_path, pagesize=letter)
            
            # 第一页
            c.drawString(100, 750, f"文档标题: {filename}")
            c.drawString(100, 700, content)
            c.drawString(100, 650, "这是用于GUI演示的测试文档")
            c.drawString(100, 600, "请使用GUI界面为此文档添加水印")
            
            # 添加更多内容
            for i in range(10):
                c.drawString(100, 550 - i * 30, f"内容行 {i + 1}: 这里是一些示例文本内容")
            
            c.showPage()
            
            # 如果是多页文档，添加第二页
            if "多页" in filename:
                c.drawString(100, 750, "第二页内容")
                c.drawString(100, 700, "继续测试多页文档的水印效果")
                
                for i in range(15):
                    c.drawString(100, 650 - i * 25, f"第二页内容行 {i + 1}")
                
                c.showPage()
            
            c.save()
            created_files.append(file_path)
            print(f"创建演示文件: {filename}")
            
        except Exception as e:
            print(f"创建文件失败 {filename}: {e}")
    
    return created_files


def show_gui_features():
    """显示GUI功能介绍"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    features_text = """
🎉 PDF水印添加工具 - GUI界面功能

✨ 主要特性:
• 直观的图形用户界面
• 拖拽式文件选择
• 实时处理进度显示
• 详细的处理日志
• 批量文件处理支持

📁 文件选择功能:
• 选择单个PDF文件
• 选择多个PDF文件
• 选择整个目录
• 文件列表预览

⚙️ 水印配置显示:
• 水印内容预览
• 字体和样式信息
• 实时字体状态检测

🔄 处理控制:
• 一键开始处理
• 实时进度条
• 可中途停止处理
• 处理结果统计

📝 日志系统:
• 详细的处理日志
• 错误信息显示
• 可清空日志记录

现在将启动GUI界面，请体验这些功能！
    """
    
    messagebox.showinfo("GUI功能介绍", features_text)
    root.destroy()


def main():
    """GUI演示主函数"""
    print("PDF水印工具 - GUI界面演示")
    print("=" * 50)
    
    try:
        # 显示功能介绍
        show_gui_features()
        
        # 创建演示文件
        print("正在创建演示文件...")
        demo_files = create_demo_files()
        
        if demo_files:
            print(f"成功创建 {len(demo_files)} 个演示文件")
            print("\n演示文件位置:")
            for file_path in demo_files:
                print(f"  - {os.path.basename(file_path)}")
        
        print("\n" + "=" * 50)
        print("启动GUI界面...")
        print("请在GUI中:")
        print("1. 点击'选择多个文件'选择刚创建的演示文件")
        print("2. 查看水印配置")
        print("3. 点击'开始处理'")
        print("4. 观察处理进度和日志")
        print("5. 检查生成的水印文件")
        print("=" * 50)
        
        # 启动GUI
        app = WatermarkGUI()
        app.run()
        
        print("\nGUI演示完成！")
        print("请检查examples目录中的输出文件:")
        
        # 显示生成的文件
        demo_dir = os.path.dirname(__file__)
        for file in os.listdir(demo_dir):
            if file.endswith('.pdf'):
                file_path = os.path.join(demo_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  {file} ({size_mb:.2f} MB)")
        
    except Exception as e:
        print(f"GUI演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
