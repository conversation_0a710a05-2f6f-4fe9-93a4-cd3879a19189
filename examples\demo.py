#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF水印工具演示脚本
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.config.watermark_config import WatermarkConfig
from src.core.pdf_processor import PDFProcessor
from src.utils.font_detector import FontDetector
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter


def create_sample_pdf(file_path: str):
    """创建示例PDF文件"""
    print(f"创建示例PDF: {file_path}")
    
    c = canvas.Canvas(file_path, pagesize=letter)
    
    # 第一页
    c.drawString(100, 750, "PDF水印工具演示文档")
    c.drawString(100, 700, "这是第一页内容")
    c.drawString(100, 650, "用于测试水印添加效果")
    c.drawString(100, 600, "")
    c.drawString(100, 550, "Demo Document for PDF Watermark Tool")
    c.drawString(100, 500, "This is the first page content")
    c.drawString(100, 450, "Used to test watermark adding effect")
    
    # 添加更多内容
    for i in range(10):
        c.drawString(100, 400 - i * 30, f"示例文本行 {i + 1} - Sample text line {i + 1}")
    
    c.showPage()
    
    # 第二页
    c.drawString(100, 750, "第二页内容")
    c.drawString(100, 700, "继续测试水印效果")
    c.drawString(100, 650, "")
    c.drawString(100, 600, "Second Page Content")
    c.drawString(100, 550, "Continue testing watermark effect")
    
    # 添加表格样式的内容
    for i in range(15):
        c.drawString(100, 500 - i * 25, f"数据行 {i + 1:02d}: 这里是一些重要的文档内容")
    
    c.showPage()
    
    # 第三页
    c.drawString(100, 750, "第三页 - 最后一页")
    c.drawString(100, 700, "测试多页水印效果")
    c.drawString(100, 650, "")
    c.drawString(100, 600, "Third Page - Final Page")
    c.drawString(100, 550, "Testing multi-page watermark effect")
    
    c.save()
    print(f"示例PDF创建完成: {file_path}")


def demo_single_file():
    """演示单文件处理"""
    print("\n=== 单文件处理演示 ===")
    
    # 创建示例文件
    demo_dir = os.path.dirname(__file__)
    sample_pdf = os.path.join(demo_dir, "sample_document.pdf")
    
    create_sample_pdf(sample_pdf)
    
    # 配置水印
    config = WatermarkConfig()
    
    # 检查字体
    font_detector = FontDetector()
    fonts = font_detector.find_kaiti_fonts()
    
    if fonts:
        best_font = font_detector.get_best_kaiti_font()
        config.font_path = best_font['path']
        print(f"使用字体: {best_font['name']}")
    else:
        print("未找到楷体字体，使用默认字体")
    
    # 创建处理器
    processor = PDFProcessor(config)
    
    # 处理文件
    print(f"开始处理: {sample_pdf}")
    success = processor.process_pdf_file(sample_pdf)
    
    if success:
        print("✓ 处理成功！")
        
        # 显示输出文件
        watermarked_path = processor._generate_output_path(sample_pdf)
        image_path = processor._generate_image_pdf_path(watermarked_path)
        
        print(f"水印PDF: {watermarked_path}")
        print(f"图片PDF: {image_path}")
        
        # 验证文件
        if os.path.exists(watermarked_path):
            size_mb = os.path.getsize(watermarked_path) / (1024 * 1024)
            print(f"水印PDF大小: {size_mb:.2f} MB")
        
        if os.path.exists(image_path):
            size_mb = os.path.getsize(image_path) / (1024 * 1024)
            print(f"图片PDF大小: {size_mb:.2f} MB")
    else:
        print("✗ 处理失败")


def demo_batch_processing():
    """演示批量处理"""
    print("\n=== 批量处理演示 ===")
    
    demo_dir = os.path.dirname(__file__)
    
    # 创建多个示例文件
    sample_files = []
    for i in range(3):
        file_path = os.path.join(demo_dir, f"batch_sample_{i + 1}.pdf")
        create_sample_pdf(file_path)
        sample_files.append(file_path)
    
    # 配置和处理器
    config = WatermarkConfig()
    font_detector = FontDetector()
    fonts = font_detector.find_kaiti_fonts()
    
    if fonts:
        best_font = font_detector.get_best_kaiti_font()
        config.font_path = best_font['path']
    
    processor = PDFProcessor(config)
    
    # 批量处理
    print(f"开始批量处理 {len(sample_files)} 个文件...")
    success_count, total_count = processor.process_multiple_files(sample_files)
    
    print(f"批量处理完成: {success_count}/{total_count} 个文件成功")


def demo_font_detection():
    """演示字体检测"""
    print("\n=== 字体检测演示 ===")
    
    font_detector = FontDetector()
    
    # 显示系统字体目录
    print("系统字体目录:")
    directories = font_detector.get_system_font_directories()
    for directory in directories:
        print(f"  {directory}")
    
    # 查找楷体字体
    print("\n查找楷体字体:")
    kaiti_fonts = font_detector.find_kaiti_fonts()
    
    if kaiti_fonts:
        for font in kaiti_fonts:
            print(f"  字体: {font['name']}")
            print(f"  路径: {font['path']}")
            print(f"  支持中文: {'是' if font['supports_chinese'] else '否'}")
            print(f"  大小: {font['size'] / 1024:.1f} KB")
            print()
        
        # 测试最佳字体
        best_font = font_detector.get_best_kaiti_font()
        print(f"推荐字体: {best_font['name']}")
        
        # 测试字体渲染
        print("测试字体渲染...")
        if font_detector.test_font_rendering(best_font['path']):
            print("✓ 字体渲染测试通过")
        else:
            print("✗ 字体渲染测试失败")
    else:
        print("  未找到楷体字体")
    
    # 显示推荐信息
    print("\n字体推荐:")
    recommendations = font_detector.get_font_recommendations()
    for rec in recommendations:
        print(f"  {rec}")


def demo_config_display():
    """演示配置显示"""
    print("\n=== 水印配置演示 ===")
    
    config = WatermarkConfig()
    
    print("当前水印配置:")
    print(f"水印内容:")
    for line in config.get_text_lines():
        print(f"  {line}")
    
    print(f"字体名称: {config.font_name}")
    print(f"字体大小: {config.font_size}")
    print(f"旋转角度: {config.rotation}°")
    print(f"透明度: {config.opacity * 100}%")
    print(f"页面比例: {config.page_ratio * 100}%")
    print(f"每页行数: {config.rows_per_page}")
    print(f"垂直偏移: {config.vertical_center_offset}mm")
    print(f"水平对齐: {config.horizontal_align}")
    print(f"垂直对齐: {config.vertical_align}")
    
    # 验证配置
    if config.validate():
        print("✓ 配置验证通过")
    else:
        print("✗ 配置验证失败")


def main():
    """主演示函数"""
    print("PDF水印工具演示程序")
    print("=" * 50)
    
    try:
        # 演示各个功能
        demo_config_display()
        demo_font_detection()
        demo_single_file()
        demo_batch_processing()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("请检查examples目录中生成的文件:")
        
        demo_dir = os.path.dirname(__file__)
        for file in os.listdir(demo_dir):
            if file.endswith('.pdf'):
                file_path = os.path.join(demo_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  {file} ({size_mb:.2f} MB)")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
