# -*- coding: utf-8 -*-
"""
PDF水印添加工具 - 现代化GUI版本
直接启动图形用户界面
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.modern_gui import ModernWatermarkGUI


def main():
    """主程序入口 - 直接启动现代化GUI"""
    try:
        print("🚀 启动PDF水印添加工具...")
        print("📱 现代化图形界面版本 v2.0")
        print("=" * 50)

        # 直接启动现代化GUI
        app = ModernWatermarkGUI()
        app.run()

    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 确保已安装所有依赖包: python install.py")
        print("2. 检查Python版本是否为3.7+")
        print("3. 确保tkinter库可用")

        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
