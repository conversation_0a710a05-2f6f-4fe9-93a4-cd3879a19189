# -*- coding: utf-8 -*-
"""
PDF水印添加工具 - 主程序入口
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.cli import CLI
from src.ui.gui import WatermarkGUI


def show_interface_choice():
    """显示界面选择"""
    print("=" * 50)
    print("    PDF水印添加工具 v1.0")
    print("=" * 50)
    print("请选择界面类型:")
    print("1. GUI图形界面 (推荐)")
    print("2. 命令行界面")
    print("0. 退出")
    print("=" * 50)

    while True:
        choice = input("请输入选择 (0-2): ").strip()

        if choice == '1':
            return 'gui'
        elif choice == '2':
            return 'cli'
        elif choice == '0':
            return 'exit'
        else:
            print("无效选择，请重新输入")


def main():
    """主程序入口"""
    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == '--gui':
                interface = 'gui'
            elif sys.argv[1] == '--cli':
                interface = 'cli'
            else:
                print("用法: python main.py [--gui|--cli]")
                sys.exit(1)
        else:
            # 显示选择界面
            interface = show_interface_choice()

        if interface == 'exit':
            print("感谢使用PDF水印添加工具！")
            return
        elif interface == 'gui':
            print("启动GUI界面...")
            app = WatermarkGUI()
            app.run()
        elif interface == 'cli':
            print("启动命令行界面...")
            cli = CLI()
            cli.run()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
