# PDF水印添加工具 - 现代化GUI使用说明

## 🎉 全新现代化界面！

您的PDF水印添加工具现在拥有了全新设计的现代化图形用户界面！

## 🚀 快速开始

### 启动程序
```bash
python main.py
```

程序将直接启动现代化GUI界面，无需选择界面类型。

## 🖥️ 界面布局

### 主界面结构
```
┌─────────────────────────────────────────────────────────────────────┐
│ 📄 PDF水印添加工具    专业的PDF水印处理解决方案    [📁][❓][⚙️]    │
├─────────────────────────────────────────────────────────────────────┤
│ 📁 文件管理                    │ 🚀 处理控制                        │
│ [📄 添加文件] [📁 添加目录]     │ 🎨 当前配置: 楷体 54号...  [修改配置] │
│ [🗑️ 清空列表]                 │ ✅ 字体就绪: STKAITI              │
│                               │                                   │
│ 📋 文件列表                    │ [🎯 开始处理]                     │
│ ┌─────────────────────────────┐ │ ████████████████ 100%             │
│ │ 📄 文件名    📊 大小  🔄 状态│ │ ✅ 准备就绪                       │
│ │ document.pdf  2.1MB  待处理 │ │                                   │
│ │ report.pdf    1.5MB  待处理 │ │ 📋 处理日志                        │
│ └─────────────────────────────┘ │ ┌─────────────────────────────────┐ │
│ 📊 共 2 个文件，总大小 3.6 MB   │ │ 🚀 PDF水印添加工具已启动         │ │
│                               │ │ 📋 使用说明:                    │ │
│                               │ │   1. 点击'添加文件'选择PDF      │ │
│                               │ │   2. 可选：修改水印配置         │ │
│                               │ │ [🗑️ 清空日志] [💾 保存日志]     │ │
│                               │ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────────┤
│ 🟢 就绪                           PDF水印添加工具 v2.0 | 现代化GUI版本 │
└─────────────────────────────────────────────────────────────────────┘
```

## 📋 详细功能说明

### 1. 顶部工具栏
- **主标题**：显示程序名称和版本
- **📁 打开输出目录**：快速打开处理结果目录
- **❓ 帮助**：显示程序信息和使用说明
- **⚙️ 配置**：打开水印配置对话框

### 2. 左侧文件管理面板

#### 文件操作按钮
- **📄 添加文件**：选择单个或多个PDF文件
- **📁 添加目录**：选择目录中的所有PDF文件
- **🗑️ 清空列表**：清空当前文件列表

#### 文件列表
- **表格显示**：文件名、大小、处理状态
- **实时更新**：显示处理进度和结果
- **选择删除**：可选中文件按Delete键删除

#### 文件统计
- 显示文件数量和总大小
- 实时更新状态信息

### 3. 右侧控制面板

#### 配置显示区域
- **当前配置**：显示水印参数摘要
- **字体状态**：显示字体检测结果
- **修改配置**：打开配置对话框

#### 处理控制
- **🎯 开始处理**：启动批量处理
- **进度条**：实时显示处理进度
- **状态标签**：显示当前处理状态

#### 日志区域
- **详细日志**：显示处理过程和结果
- **自动滚动**：自动滚动到最新日志
- **日志控制**：清空和保存日志功能

## ⚙️ 水印配置对话框

点击"修改配置"打开配置对话框，包含三个标签页：

### 📝 基本设置
- **水印内容**：多行文本输入框
- **字体大小**：滑块调节（20-100）
- **透明度**：滑块调节（10%-100%）

### ⚙️ 高级设置
- **旋转角度**：滑块调节（0-360°）
- **每页行数**：数值选择（1-10）
- **页面比例**：滑块调节（20%-100%）
- **垂直偏移**：精确调节（-50mm到50mm）

### 👁️ 预览
- **配置预览**：显示当前所有设置
- **效果说明**：水印显示效果描述

## 🎯 使用流程

### 步骤1：添加文件
1. 点击"📄 添加文件"选择PDF文件
2. 或点击"📁 添加目录"选择整个目录
3. 文件将显示在列表中，可查看大小和状态

### 步骤2：配置水印（可选）
1. 查看当前配置显示
2. 如需修改，点击"修改配置"
3. 在对话框中调整各项参数
4. 点击"确定"应用设置

### 步骤3：开始处理
1. 点击"🎯 开始处理"按钮
2. 确认处理对话框
3. 观察进度条和日志输出
4. 等待处理完成

### 步骤4：查看结果
1. 处理完成后显示结果对话框
2. 点击"📁 打开输出目录"查看文件
3. 检查生成的水印PDF文件

## ✨ 界面特色功能

### 🎨 现代化设计
- **美观界面**：现代化的视觉设计
- **直观布局**：左右分栏，功能区域清晰
- **图标标识**：丰富的emoji图标提升用户体验

### 🔄 实时反馈
- **进度显示**：实时进度条和百分比
- **状态更新**：文件处理状态实时更新
- **详细日志**：完整的处理过程记录

### 🛠️ 便捷操作
- **快捷键支持**：
  - `Ctrl+O`：添加文件
  - `Ctrl+D`：添加目录
  - `Ctrl+R`：开始处理
  - `Delete`：删除选中文件
  - `F1`：显示帮助

### 🔧 智能功能
- **文件验证**：自动验证PDF文件有效性
- **错误处理**：友好的错误提示和处理
- **状态管理**：智能的按钮状态管理

## 📊 输出文件说明

处理完成后，每个PDF文件会生成两个输出文件：

1. **`原文件名_watermarked.pdf`**
   - 添加了水印的PDF文件
   - 保持原始PDF格式

2. **`原文件名_watermarked_image.pdf`** ⭐ 推荐
   - 转换为图片型的PDF文件
   - 水印无法被轻易移除
   - 推荐使用此文件

## 🔧 故障排除

### 常见问题
1. **程序无法启动**
   - 检查Python版本（需要3.7+）
   - 运行 `python install.py` 安装依赖

2. **字体显示异常**
   - 程序会自动检测楷体字体
   - 如无楷体，会使用默认字体

3. **处理失败**
   - 检查PDF文件是否损坏或加密
   - 确保有足够磁盘空间
   - 查看日志获取详细错误信息

### 获取帮助
- 点击界面中的"❓ 帮助"按钮
- 查看处理日志中的详细信息
- 使用"💾 保存日志"功能保存错误信息

## 🎊 总结

现在您拥有了一个功能完整、界面现代化的PDF水印添加工具：

✅ **直接启动GUI** - 无需选择界面类型
✅ **现代化设计** - 美观直观的用户界面  
✅ **分栏布局** - 左侧文件管理，右侧处理控制
✅ **标签页配置** - 分类清晰的配置对话框
✅ **实时反馈** - 进度条、状态更新、详细日志
✅ **便捷操作** - 快捷键、拖拽支持、智能提示
✅ **完整功能** - 所有原有功能完美保留

立即体验现代化GUI：
```bash
python main.py
```

享受全新的PDF水印处理体验！🚀
