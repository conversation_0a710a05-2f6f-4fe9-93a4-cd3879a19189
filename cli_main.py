# -*- coding: utf-8 -*-
"""
PDF水印添加工具 - 命令行版本
备用的命令行界面启动脚本
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.cli import CLI


def main():
    """命令行版本主程序入口"""
    try:
        print("💻 启动PDF水印添加工具 - 命令行版本")
        print("=" * 50)
        
        cli = CLI()
        cli.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
