"""
水印编辑器 - 实时预览界面
提供左侧配置面板和右侧实时预览功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageDraw, ImageFont, ImageTk
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.config.watermark_config import WatermarkConfig

class WatermarkEditor:
    """水印编辑器主类"""
    
    def __init__(self, config: WatermarkConfig):
        self.config = config
        self.root = tk.Tk()
        self.root.title("编辑水印")
        self.root.geometry("1200x800")
        
        # 预览相关
        self.preview_photo = None
        self.updating = False  # 防止无限循环
        
        self.create_widgets()
        self.load_config()
        
        # 延迟更新预览
        self.root.after(300, self.update_preview)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 使用PanedWindow分割左右
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧配置面板
        left_frame = ttk.Frame(paned)
        paned.add(left_frame, weight=1)
        self.create_config_panel(left_frame)
        
        # 右侧预览面板
        right_frame = ttk.Frame(paned)
        paned.add(right_frame, weight=2)
        self.create_preview_panel(right_frame)
        
        # 底部按钮
        self.create_buttons(main_frame)
    
    def create_config_panel(self, parent):
        """创建配置面板"""
        # 源文本
        source_group = ttk.LabelFrame(parent, text="来源", padding=10)
        source_group.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Radiobutton(source_group, text="文本(X):", value="text").pack(anchor=tk.W)
        
        self.text_widget = tk.Text(source_group, height=4, width=40)
        self.text_widget.pack(fill=tk.X, pady=5)
        self.text_widget.bind('<KeyRelease>', self.on_text_change)
        
        # 字体设置
        font_frame = ttk.Frame(source_group)
        font_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(font_frame, text="字体(N):").pack(side=tk.LEFT)
        self.font_var = tk.StringVar(value="楷体")
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_var, width=15)
        font_combo['values'] = ["楷体", "宋体", "黑体", "仿宋", "微软雅黑"]
        font_combo.pack(side=tk.LEFT, padx=(5, 10))
        font_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        ttk.Label(font_frame, text="字号(S):").pack(side=tk.LEFT)
        self.size_var = tk.StringVar(value="24")
        size_combo = ttk.Combobox(font_frame, textvariable=self.size_var, width=8)
        size_combo['values'] = [str(i) for i in range(8, 73)]
        size_combo.pack(side=tk.LEFT, padx=5)
        size_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 外观设置
        appearance_group = ttk.LabelFrame(parent, text="外观", padding=10)
        appearance_group.pack(fill=tk.X, pady=(0, 10))
        
        # 旋转
        rotation_frame = ttk.Frame(appearance_group)
        rotation_frame.pack(fill=tk.X, pady=5)
        ttk.Label(rotation_frame, text="旋转:").pack(side=tk.LEFT)
        self.rotation_var = tk.StringVar(value="顺时针45°")
        rotation_combo = ttk.Combobox(rotation_frame, textvariable=self.rotation_var, width=15)
        rotation_combo['values'] = ["顺时针45°", "逆时针45°", "水平", "垂直"]
        rotation_combo.pack(side=tk.LEFT, padx=5)
        rotation_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 透明度
        opacity_frame = ttk.Frame(appearance_group)
        opacity_frame.pack(fill=tk.X, pady=5)
        ttk.Label(opacity_frame, text="不透明度(Y):").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=8)
        opacity_scale = ttk.Scale(opacity_frame, from_=1, to=100, variable=self.opacity_var, orient=tk.HORIZONTAL)
        opacity_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        opacity_scale.configure(command=self.on_opacity_change)
        self.opacity_label = ttk.Label(opacity_frame, text="8%")
        self.opacity_label.pack(side=tk.RIGHT)
        
        # 页面比例
        ratio_frame = ttk.Frame(appearance_group)
        ratio_frame.pack(fill=tk.X, pady=5)
        ttk.Checkbutton(ratio_frame, text="相对页面比例(L):").pack(side=tk.LEFT)
        self.ratio_var = tk.StringVar(value="50%")
        ratio_combo = ttk.Combobox(ratio_frame, textvariable=self.ratio_var, width=10)
        ratio_combo['values'] = ["30%", "40%", "50%", "60%", "70%"]
        ratio_combo.pack(side=tk.LEFT, padx=5)
        ratio_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 行数
        rows_frame = ttk.Frame(appearance_group)
        rows_frame.pack(fill=tk.X, pady=5)
        ttk.Checkbutton(rows_frame, text="多行水印(H):").pack(side=tk.LEFT)
        self.rows_var = tk.StringVar(value="一页三行")
        rows_combo = ttk.Combobox(rows_frame, textvariable=self.rows_var, width=15)
        rows_combo['values'] = ["一页一行", "一页二行", "一页三行", "一页四行", "一页五行"]
        rows_combo.pack(side=tk.LEFT, padx=5)
        rows_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 位置设置
        position_group = ttk.LabelFrame(parent, text="位置", padding=10)
        position_group.pack(fill=tk.X, pady=(0, 10))
        
        # 垂直对齐
        v_align_frame = ttk.Frame(position_group)
        v_align_frame.pack(fill=tk.X, pady=5)
        ttk.Label(v_align_frame, text="垂直对齐(T):").pack(side=tk.LEFT)
        self.v_align_var = tk.StringVar(value="居中")
        v_align_combo = ttk.Combobox(v_align_frame, textvariable=self.v_align_var, width=10)
        v_align_combo['values'] = ["顶部", "居中", "底部"]
        v_align_combo.pack(side=tk.LEFT, padx=5)
        v_align_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 水平对齐
        h_align_frame = ttk.Frame(position_group)
        h_align_frame.pack(fill=tk.X, pady=5)
        ttk.Label(h_align_frame, text="水平对齐(Z):").pack(side=tk.LEFT)
        self.h_align_var = tk.StringVar(value="居中")
        h_align_combo = ttk.Combobox(h_align_frame, textvariable=self.h_align_var, width=10)
        h_align_combo['values'] = ["左侧", "居中", "右侧"]
        h_align_combo.pack(side=tk.LEFT, padx=5)
        h_align_combo.bind('<<ComboboxSelected>>', self.on_config_change)
        
        # 应用于
        apply_frame = ttk.Frame(position_group)
        apply_frame.pack(fill=tk.X, pady=5)
        ttk.Label(apply_frame, text="应用于(A):").pack(side=tk.LEFT)
        self.apply_var = tk.StringVar(value="全部页面")
        apply_combo = ttk.Combobox(apply_frame, textvariable=self.apply_var, width=15)
        apply_combo['values'] = ["全部页面", "当前页面", "指定页面"]
        apply_combo.pack(side=tk.LEFT, padx=5)
        apply_combo.bind('<<ComboboxSelected>>', self.on_config_change)
    
    def create_preview_panel(self, parent):
        """创建预览面板"""
        ttk.Label(parent, text="预览:", font=("微软雅黑", 14, "bold")).pack(anchor=tk.W, pady=(0, 10))
        
        # 预览画布
        canvas_frame = ttk.Frame(parent, relief=tk.SUNKEN, borderwidth=2)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(canvas_frame, bg="white")
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 页面信息
        info_frame = ttk.Frame(parent)
        info_frame.pack(fill=tk.X, pady=(10, 0))
        ttk.Label(info_frame, text="第 1 页 (预览)", font=("微软雅黑", 10)).pack()
    
    def create_buttons(self, parent):
        """创建按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0), side=tk.BOTTOM)
        
        ttk.Button(button_frame, text="取消", command=self.cancel, width=10).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="保存", command=self.save_config, width=10).pack(side=tk.RIGHT, padx=(0, 5))
    
    def load_config(self):
        """加载配置到界面"""
        self.text_widget.delete("1.0", tk.END)
        self.text_widget.insert("1.0", self.config.text)
        self.size_var.set(str(self.config.font_size))
        self.opacity_var.set(self.config.opacity * 100)
        self.opacity_label.config(text=f"{int(self.config.opacity * 100)}%")
        
        # 设置行数
        rows_map = {1: "一页一行", 2: "一页二行", 3: "一页三行", 4: "一页四行", 5: "一页五行"}
        self.rows_var.set(rows_map.get(self.config.rows_per_page, "一页三行"))
        
        # 设置页面比例
        self.ratio_var.set(f"{int(self.config.page_ratio * 100)}%")
    
    def on_text_change(self, event=None):
        """文本变化事件"""
        if not self.updating:
            self.root.after(100, self.update_preview)
    
    def on_config_change(self, event=None):
        """配置变化事件"""
        if not self.updating:
            self.root.after(50, self.update_preview)
    
    def on_opacity_change(self, value):
        """透明度变化事件"""
        self.opacity_label.config(text=f"{int(float(value))}%")
        if not self.updating:
            self.root.after(50, self.update_preview)
    
    def update_preview(self):
        """更新预览"""
        if self.updating:
            return

        self.updating = True

        try:
            print("🔄 更新预览...")

            # 创建预览图像
            img_width = 350
            img_height = 450
            img = Image.new('RGB', (img_width, img_height), 'white')

            # 获取配置
            text = self.text_widget.get("1.0", tk.END).strip()
            if not text:
                text = "水印预览"

            font_size = int(self.size_var.get() or 24)
            opacity = int(self.opacity_var.get())

            # 获取行数
            rows_map = {"一页一行": 1, "一页二行": 2, "一页三行": 3, "一页四行": 4, "一页五行": 5}
            rows = rows_map.get(self.rows_var.get(), 3)

            # 加载字体
            font = self.load_font(font_size)

            # 绘制水印
            self.draw_watermarks(img, text, font, opacity, rows)

            # 显示预览
            self.preview_photo = ImageTk.PhotoImage(img)
            self.canvas.delete("all")

            # 居中显示
            self.canvas.update()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            if canvas_width > 1 and canvas_height > 1:
                center_x = canvas_width // 2
                center_y = canvas_height // 2
            else:
                center_x = 250
                center_y = 300

            self.canvas.create_image(center_x, center_y, image=self.preview_photo)

            # 添加边框
            x1 = center_x - img_width // 2
            y1 = center_y - img_height // 2
            x2 = center_x + img_width // 2
            y2 = center_y + img_height // 2
            self.canvas.create_rectangle(x1, y1, x2, y2, outline="gray", width=2)

            print("✅ 预览更新完成")

        except Exception as e:
            print(f"❌ 预览更新失败: {e}")
            self.canvas.delete("all")
            self.canvas.create_text(250, 300, text=f"预览失败: {str(e)}", fill="red")

        finally:
            self.updating = False

    def load_font(self, size):
        """加载字体"""
        font_paths = [
            "C:/Windows/Fonts/STKAITI.TTF",
            "C:/Windows/Fonts/simkai.ttf",
            "C:/Windows/Fonts/kaiti.ttf",
            "C:/Windows/Fonts/msyh.ttc"
        ]

        for path in font_paths:
            try:
                return ImageFont.truetype(path, size)
            except:
                continue

        return ImageFont.load_default()

    def draw_watermarks(self, img, text, font, opacity, rows):
        """绘制水印"""
        img_width, img_height = img.size
        margin = 40
        available_height = img_height - 2 * margin
        row_height = available_height / rows if rows > 0 else available_height

        for row in range(rows):
            y = margin + row * row_height + row_height / 3

            # 交错排列
            if row % 2 == 0:
                x = margin + (img_width - 2 * margin) * 0.2
            else:
                x = margin + (img_width - 2 * margin) * 0.6

            # 绘制水印
            self.draw_single_watermark(img, text, font, x, y, opacity)

    def draw_single_watermark(self, img, text, font, x, y, opacity):
        """绘制单个水印"""
        lines = text.split('\n')
        line_height = font.size + 5

        # 创建临时图像
        temp_img = Image.new('RGBA', (300, 200), (255, 255, 255, 0))
        temp_draw = ImageDraw.Draw(temp_img)

        # 绘制每行文本
        for i, line in enumerate(lines):
            if line.strip():
                color = (128, 128, 128, int(255 * opacity / 100))
                temp_draw.text((20, 20 + i * line_height), line, font=font, fill=color)

        # 旋转45度
        temp_img = temp_img.rotate(45, expand=True)

        # 粘贴到主图像
        try:
            img.paste(temp_img, (int(x), int(y)), temp_img)
        except:
            pass

    def save_config(self):
        """保存配置"""
        try:
            # 更新配置
            self.config.text = self.text_widget.get("1.0", tk.END).strip()
            self.config.font_size = int(self.size_var.get() or 24)
            self.config.opacity = self.opacity_var.get() / 100

            # 更新行数
            rows_map = {"一页一行": 1, "一页二行": 2, "一页三行": 3, "一页四行": 4, "一页五行": 5}
            self.config.rows_per_page = rows_map.get(self.rows_var.get(), 3)

            # 更新页面比例
            ratio_text = self.ratio_var.get()
            ratio_value = int(ratio_text.replace('%', '')) / 100
            self.config.page_ratio = ratio_value

            # 重新初始化配置
            self.config.__post_init__()

            messagebox.showinfo("成功", "水印配置已保存！")
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")

    def cancel(self):
        """取消"""
        self.root.destroy()

    def run(self):
        """运行编辑器"""
        self.root.mainloop()
