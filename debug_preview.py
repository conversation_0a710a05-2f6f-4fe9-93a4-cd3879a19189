#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试预览功能
"""

import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageDraw, ImageFont, ImageTk
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config.watermark_config import WatermarkConfig

class DebugPreview:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("调试预览")
        self.root.geometry("800x600")
        
        self.config = WatermarkConfig()
        self.create_widgets()
        self.update_preview()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 文本输入
        ttk.Label(left_frame, text="水印文本:").pack(anchor=tk.W)
        self.text_widget = tk.Text(left_frame, height=4, width=30)
        self.text_widget.pack(fill=tk.X, pady=5)
        self.text_widget.insert("1.0", "小红书ID：11491392150\n关注保研喵学姐\n更多免费资料分享")
        self.text_widget.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 更新按钮
        ttk.Button(left_frame, text="手动更新预览", command=self.update_preview).pack(pady=10)
        
        # 右侧预览
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        ttk.Label(right_frame, text="预览:", font=("微软雅黑", 12)).pack(anchor=tk.W)
        
        # 预览画布
        self.canvas = tk.Canvas(right_frame, bg="white", width=400, height=500)
        self.canvas.pack(fill=tk.BOTH, expand=True, pady=10)
    
    def update_preview(self):
        """更新预览"""
        print("🔄 开始更新预览...")
        
        try:
            # 固定尺寸
            img_width = 300
            img_height = 400
            
            # 创建白色背景图像
            img = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 获取文本
            text = self.text_widget.get("1.0", tk.END).strip()
            if not text:
                text = "测试水印"
            
            print(f"📝 水印文本: {text}")
            
            # 加载字体
            font_size = 24
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/STKAITI.TTF", font_size)
                print("✅ 字体加载成功")
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/simkai.ttf", font_size)
                    print("✅ 备用字体加载成功")
                except:
                    font = ImageFont.load_default()
                    print("⚠️ 使用默认字体")
            
            # 绘制水印
            lines = text.split('\n')
            line_height = font_size + 5
            
            # 计算3个位置
            positions = [
                (50, 100),
                (200, 200),
                (80, 300)
            ]
            
            for pos_x, pos_y in positions:
                # 创建临时图像
                temp_img = Image.new('RGBA', (250, 150), (255, 255, 255, 0))
                temp_draw = ImageDraw.Draw(temp_img)
                
                # 绘制每行文本
                for i, line in enumerate(lines):
                    if line.strip():
                        color = (128, 128, 128, 180)  # 半透明灰色
                        temp_draw.text((10, 10 + i * line_height), line, font=font, fill=color)
                
                # 旋转45度
                temp_img = temp_img.rotate(45, expand=True)
                
                # 粘贴到主图像
                img.paste(temp_img, (pos_x, pos_y), temp_img)
            
            print("🎨 水印绘制完成")
            
            # 显示在画布上
            self.photo = ImageTk.PhotoImage(img)
            self.canvas.delete("all")
            
            # 居中显示
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                center_x = canvas_width // 2
                center_y = canvas_height // 2
            else:
                center_x = 200
                center_y = 250
            
            self.canvas.create_image(center_x, center_y, image=self.photo)
            
            # 添加边框
            x1 = center_x - img_width // 2
            y1 = center_y - img_height // 2
            x2 = center_x + img_width // 2
            y2 = center_y + img_height // 2
            self.canvas.create_rectangle(x1, y1, x2, y2, outline="gray", width=2)
            
            print("✅ 预览更新完成")
            
        except Exception as e:
            print(f"❌ 预览更新失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 显示错误信息
            self.canvas.delete("all")
            self.canvas.create_text(200, 250, text=f"预览失败: {str(e)}", fill="red")
    
    def run(self):
        self.root.mainloop()

def main():
    print("🚀 启动预览调试...")
    app = DebugPreview()
    app.run()

if __name__ == "__main__":
    main()
