# -*- coding: utf-8 -*-
"""
水印功能测试
"""

import os
import sys
import unittest
import tempfile
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../src'))

from src.config.watermark_config import WatermarkConfig
from src.core.watermark_engine import WatermarkEngine
from src.core.pdf_processor import PDFProcessor
from src.utils.font_detector import FontDetector


class TestWatermarkConfig(unittest.TestCase):
    """测试水印配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = WatermarkConfig()
        
        # 检查基本属性
        self.assertIsNotNone(config.text)
        self.assertEqual(config.font_size, 54)
        self.assertEqual(config.rotation, 45.0)
        self.assertEqual(config.opacity, 0.2)
        self.assertEqual(config.rows_per_page, 3)
        
        # 检查配置验证
        self.assertTrue(config.validate())
    
    def test_text_lines(self):
        """测试文本行分割"""
        config = WatermarkConfig()
        lines = config.get_text_lines()
        
        self.assertEqual(len(lines), 3)
        self.assertIn("小红书ID：11491392150", lines[0])
        self.assertIn("关注保研喵学姐", lines[1])
        self.assertIn("更多免费资料分享", lines[2])
    
    def test_unit_conversion(self):
        """测试单位转换"""
        config = WatermarkConfig()
        
        # 测试毫米转点
        points = config.mm_to_points(11.0)
        self.assertAlmostEqual(points, 31.18, places=1)


class TestFontDetector(unittest.TestCase):
    """测试字体检测"""
    
    def setUp(self):
        self.font_detector = FontDetector()
    
    def test_system_font_directories(self):
        """测试系统字体目录获取"""
        directories = self.font_detector.get_system_font_directories()
        self.assertIsInstance(directories, list)
        self.assertGreater(len(directories), 0)
    
    def test_kaiti_font_search(self):
        """测试楷体字体搜索"""
        fonts = self.font_detector.find_kaiti_fonts()
        self.assertIsInstance(fonts, list)
        
        # 如果找到字体，检查字体信息
        if fonts:
            font = fonts[0]
            self.assertIn('name', font)
            self.assertIn('path', font)
            self.assertIn('supports_chinese', font)
            self.assertTrue(os.path.exists(font['path']))


class TestWatermarkEngine(unittest.TestCase):
    """测试水印引擎"""
    
    def setUp(self):
        self.config = WatermarkConfig()
        self.engine = WatermarkEngine(self.config)
    
    def test_watermark_creation(self):
        """测试水印创建"""
        # 创建测试水印
        watermark_bytes = self.engine.create_watermark_pdf(595, 842)  # A4尺寸
        
        self.assertIsInstance(watermark_bytes, bytes)
        self.assertGreater(len(watermark_bytes), 100)  # 应该有一定大小
    
    def test_position_calculation(self):
        """测试位置计算"""
        positions = self.engine._calculate_watermark_positions(595, 842)
        
        self.assertIsInstance(positions, list)
        self.assertEqual(len(positions), self.config.rows_per_page)
        
        # 检查位置坐标
        for x, y in positions:
            self.assertIsInstance(x, (int, float))
            self.assertIsInstance(y, (int, float))
            self.assertGreater(x, 0)
            self.assertGreater(y, 0)


class TestPDFProcessor(unittest.TestCase):
    """测试PDF处理器"""
    
    def setUp(self):
        self.config = WatermarkConfig()
        self.processor = PDFProcessor(self.config)
    
    def test_output_path_generation(self):
        """测试输出路径生成"""
        input_path = "/test/document.pdf"
        output_path = self.processor._generate_output_path(input_path)
        
        self.assertTrue(output_path.endswith("_watermarked.pdf"))
        self.assertIn("document", output_path)
    
    def test_image_pdf_path_generation(self):
        """测试图片PDF路径生成"""
        watermarked_path = "/test/document_watermarked.pdf"
        image_path = self.processor._generate_image_pdf_path(watermarked_path)
        
        self.assertTrue(image_path.endswith("_image.pdf"))
        self.assertIn("document_watermarked", image_path)


def create_test_pdf(file_path: str):
    """创建测试PDF文件"""
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    
    c = canvas.Canvas(file_path, pagesize=letter)
    c.drawString(100, 750, "这是一个测试PDF文件")
    c.drawString(100, 700, "用于测试水印添加功能")
    c.drawString(100, 650, "Test PDF Document")
    c.drawString(100, 600, "For watermark testing")
    c.showPage()
    c.save()


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        self.config = WatermarkConfig()
        self.processor = PDFProcessor(self.config)
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 创建测试PDF
        test_pdf = os.path.join(self.temp_dir, "test.pdf")
        create_test_pdf(test_pdf)
        
        self.assertTrue(os.path.exists(test_pdf))
        
        # 处理PDF（如果有可用字体）
        font_detector = FontDetector()
        fonts = font_detector.find_kaiti_fonts()
        
        if fonts:
            # 更新字体配置
            best_font = font_detector.get_best_kaiti_font()
            self.config.font_path = best_font['path']
            self.processor = PDFProcessor(self.config)
            
            # 处理文件
            success = self.processor.process_pdf_file(test_pdf)
            
            if success:
                # 检查输出文件
                watermarked_path = self.processor._generate_output_path(test_pdf)
                image_path = self.processor._generate_image_pdf_path(watermarked_path)
                
                self.assertTrue(os.path.exists(watermarked_path))
                self.assertTrue(os.path.exists(image_path))
                
                # 检查文件大小
                self.assertGreater(os.path.getsize(watermarked_path), 0)
                self.assertGreater(os.path.getsize(image_path), 0)
        else:
            self.skipTest("未找到可用的楷体字体")


def run_tests():
    """运行所有测试"""
    print("开始运行PDF水印工具测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestWatermarkConfig))
    test_suite.addTest(unittest.makeSuite(TestFontDetector))
    test_suite.addTest(unittest.makeSuite(TestWatermarkEngine))
    test_suite.addTest(unittest.makeSuite(TestPDFProcessor))
    test_suite.addTest(unittest.makeSuite(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    print(f"\n测试完成:")
    print(f"运行: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_tests()
