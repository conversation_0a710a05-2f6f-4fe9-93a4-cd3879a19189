# -*- coding: utf-8 -*-
"""
字体检测模块
负责检测和处理中文字体
"""

import os
import platform
import subprocess
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import requests
from fonttools.ttLib import TTFont


class FontDetector:
    """字体检测器类"""
    
    def __init__(self):
        """初始化字体检测器"""
        self.system = platform.system()
        self.font_cache = {}
        self.chinese_test_text = "小红书ID：11491392150 关注保研喵学姐 更多免费资料分享"
    
    def get_system_font_directories(self) -> List[str]:
        """
        获取系统字体目录
        
        Returns:
            字体目录列表
        """
        directories = []
        
        if self.system == "Windows":
            directories.extend([
                "C:/Windows/Fonts",
                os.path.expanduser("~/AppData/Local/Microsoft/Windows/Fonts")
            ])
        elif self.system == "Darwin":  # macOS
            directories.extend([
                "/System/Library/Fonts",
                "/Library/Fonts",
                os.path.expanduser("~/Library/Fonts")
            ])
        else:  # Linux
            directories.extend([
                "/usr/share/fonts",
                "/usr/local/share/fonts",
                os.path.expanduser("~/.fonts"),
                os.path.expanduser("~/.local/share/fonts")
            ])
        
        # 添加项目字体目录
        project_font_dir = os.path.join(os.path.dirname(__file__), "../../fonts")
        if os.path.exists(project_font_dir):
            directories.append(project_font_dir)
        
        return [d for d in directories if os.path.exists(d)]
    
    def find_kaiti_fonts(self) -> List[Dict[str, str]]:
        """
        查找楷体字体
        
        Returns:
            楷体字体信息列表
        """
        kaiti_fonts = []
        font_dirs = self.get_system_font_directories()
        
        # 楷体字体文件名模式
        kaiti_patterns = [
            "simkai.ttf", "SIMKAI.TTF",
            "kaiti.ttf", "KAITI.TTF",
            "楷体.ttf", "楷体.TTF",
            "STKaiti.ttf", "STKaiti.TTF",
            "DFKai-SB.ttf", "DFKai-SB.TTF"
        ]
        
        for font_dir in font_dirs:
            try:
                for file_name in os.listdir(font_dir):
                    if any(pattern.lower() in file_name.lower() for pattern in kaiti_patterns):
                        font_path = os.path.join(font_dir, file_name)
                        if os.path.isfile(font_path):
                            font_info = self._get_font_info(font_path)
                            if font_info:
                                kaiti_fonts.append(font_info)
            except (OSError, PermissionError):
                continue
        
        return kaiti_fonts
    
    def _get_font_info(self, font_path: str) -> Optional[Dict[str, str]]:
        """
        获取字体信息
        
        Args:
            font_path: 字体文件路径
            
        Returns:
            字体信息字典
        """
        try:
            font = TTFont(font_path)
            
            # 获取字体名称
            name_table = font['name']
            font_name = ""
            
            # 尝试获取中文名称
            for record in name_table.names:
                if record.nameID == 1:  # Font Family Name
                    if record.platformID == 3 and record.platEncID == 1:  # Windows Unicode
                        font_name = record.toUnicode()
                        break
                    elif record.platformID == 1:  # Macintosh
                        font_name = record.toUnicode()
            
            if not font_name:
                font_name = os.path.splitext(os.path.basename(font_path))[0]
            
            # 检查是否支持中文
            supports_chinese = self._check_chinese_support(font)
            
            return {
                'name': font_name,
                'path': font_path,
                'file_name': os.path.basename(font_path),
                'supports_chinese': supports_chinese,
                'size': os.path.getsize(font_path)
            }
            
        except Exception as e:
            print(f"读取字体信息失败 {font_path}: {e}")
            return None
    
    def _check_chinese_support(self, font: TTFont) -> bool:
        """
        检查字体是否支持中文
        
        Args:
            font: TTFont对象
            
        Returns:
            是否支持中文
        """
        try:
            cmap = font.getBestCmap()
            if not cmap:
                return False
            
            # 检查常用中文字符
            chinese_chars = ['中', '文', '字', '体', '测', '试']
            supported_count = 0
            
            for char in chinese_chars:
                if ord(char) in cmap:
                    supported_count += 1
            
            # 如果支持一半以上的测试字符，认为支持中文
            return supported_count >= len(chinese_chars) // 2
            
        except Exception:
            return False
    
    def get_best_kaiti_font(self) -> Optional[Dict[str, str]]:
        """
        获取最佳楷体字体
        
        Returns:
            最佳楷体字体信息
        """
        kaiti_fonts = self.find_kaiti_fonts()
        
        if not kaiti_fonts:
            return None
        
        # 优先选择支持中文的字体
        chinese_fonts = [f for f in kaiti_fonts if f['supports_chinese']]
        if chinese_fonts:
            # 按文件大小排序，选择较大的（通常包含更多字符）
            chinese_fonts.sort(key=lambda x: x['size'], reverse=True)
            return chinese_fonts[0]
        
        # 如果没有明确支持中文的，选择第一个
        return kaiti_fonts[0]
    
    def download_kaiti_font(self, download_dir: str = None) -> Optional[str]:
        """
        下载楷体字体文件
        
        Args:
            download_dir: 下载目录
            
        Returns:
            下载的字体文件路径
        """
        if not download_dir:
            download_dir = os.path.join(os.path.dirname(__file__), "../../fonts")
        
        os.makedirs(download_dir, exist_ok=True)
        
        # 楷体字体下载链接（示例，实际使用时需要合法的字体文件）
        font_urls = [
            {
                'name': 'simkai.ttf',
                'url': 'https://github.com/adobe-fonts/source-han-serif/releases/download/2.001R/09_SourceHanSerifSC.zip'
            }
        ]
        
        for font_info in font_urls:
            try:
                font_path = os.path.join(download_dir, font_info['name'])
                
                if os.path.exists(font_path):
                    print(f"字体文件已存在: {font_path}")
                    return font_path
                
                print(f"正在下载字体: {font_info['name']}")
                
                response = requests.get(font_info['url'], stream=True, timeout=30)
                response.raise_for_status()
                
                with open(font_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print(f"字体下载完成: {font_path}")
                return font_path
                
            except Exception as e:
                print(f"下载字体失败: {e}")
                continue
        
        return None
    
    def test_font_rendering(self, font_path: str) -> bool:
        """
        测试字体渲染效果
        
        Args:
            font_path: 字体文件路径
            
        Returns:
            测试是否通过
        """
        try:
            from reportlab.pdfgen import canvas
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import io
            
            # 注册字体
            font_name = "TestKaiti"
            pdfmetrics.registerFont(TTFont(font_name, font_path))
            
            # 创建测试PDF
            packet = io.BytesIO()
            can = canvas.Canvas(packet, pagesize=(200, 100))
            
            # 设置字体并绘制测试文本
            can.setFont(font_name, 12)
            can.drawString(10, 50, self.chinese_test_text[:20])  # 只测试前20个字符
            can.save()
            
            # 检查生成的PDF大小
            pdf_size = len(packet.getvalue())
            
            print(f"字体渲染测试通过: {font_path}")
            print(f"测试PDF大小: {pdf_size} 字节")
            
            return pdf_size > 1000  # 如果PDF太小，可能渲染失败
            
        except Exception as e:
            print(f"字体渲染测试失败: {e}")
            return False
    
    def get_font_recommendations(self) -> List[str]:
        """
        获取字体推荐
        
        Returns:
            推荐信息列表
        """
        recommendations = []
        
        # 检查系统字体
        kaiti_fonts = self.find_kaiti_fonts()
        
        if kaiti_fonts:
            best_font = self.get_best_kaiti_font()
            recommendations.append(f"✓ 找到楷体字体: {best_font['name']}")
            recommendations.append(f"  路径: {best_font['path']}")
            recommendations.append(f"  支持中文: {'是' if best_font['supports_chinese'] else '否'}")
        else:
            recommendations.append("✗ 未找到楷体字体")
            recommendations.append("建议:")
            recommendations.append("  1. 在Windows系统中安装楷体字体")
            recommendations.append("  2. 将楷体字体文件放入项目的fonts目录")
            recommendations.append("  3. 使用程序的字体下载功能")
        
        return recommendations
    
    def setup_project_fonts(self) -> bool:
        """
        设置项目字体
        
        Returns:
            设置是否成功
        """
        try:
            fonts_dir = os.path.join(os.path.dirname(__file__), "../../fonts")
            os.makedirs(fonts_dir, exist_ok=True)
            
            # 检查是否已有字体文件
            existing_fonts = self.find_kaiti_fonts()
            if existing_fonts:
                print("已找到可用的楷体字体")
                return True
            
            # 尝试从系统复制字体
            system_fonts = self.find_kaiti_fonts()
            if system_fonts:
                best_font = self.get_best_kaiti_font()
                target_path = os.path.join(fonts_dir, "kaiti.ttf")
                
                import shutil
                shutil.copy2(best_font['path'], target_path)
                print(f"已复制字体到项目目录: {target_path}")
                return True
            
            print("未找到可用的楷体字体，请手动安装")
            return False
            
        except Exception as e:
            print(f"设置项目字体失败: {e}")
            return False
