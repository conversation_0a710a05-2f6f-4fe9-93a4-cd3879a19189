#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的水印编辑器测试
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageDraw, ImageFont, ImageTk
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config.watermark_config import WatermarkConfig

class SimpleWatermarkEditor:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简单水印编辑器")
        self.root.geometry("1000x700")
        
        self.config = WatermarkConfig()
        self.preview_width = 300
        self.preview_height = 400
        
        self.create_widgets()
        self.update_preview()
    
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 水印文本
        ttk.Label(left_frame, text="水印文本:").pack(anchor=tk.W)
        self.text_widget = tk.Text(left_frame, height=4, width=30)
        self.text_widget.pack(fill=tk.X, pady=5)
        self.text_widget.insert("1.0", self.config.text)
        self.text_widget.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 字体大小
        size_frame = ttk.Frame(left_frame)
        size_frame.pack(fill=tk.X, pady=5)
        ttk.Label(size_frame, text="字体大小:").pack(side=tk.LEFT)
        self.size_var = tk.StringVar(value=str(self.config.font_size))
        size_spin = ttk.Spinbox(size_frame, from_=8, to=72, textvariable=self.size_var, width=10)
        size_spin.pack(side=tk.RIGHT)
        size_spin.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 透明度
        opacity_frame = ttk.Frame(left_frame)
        opacity_frame.pack(fill=tk.X, pady=5)
        ttk.Label(opacity_frame, text="透明度:").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=self.config.opacity * 100)
        opacity_scale = ttk.Scale(opacity_frame, from_=1, to=100, variable=self.opacity_var, orient=tk.HORIZONTAL)
        opacity_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        opacity_scale.configure(command=lambda v: self.update_preview())
        
        # 行数
        rows_frame = ttk.Frame(left_frame)
        rows_frame.pack(fill=tk.X, pady=5)
        ttk.Label(rows_frame, text="行数:").pack(side=tk.LEFT)
        self.rows_var = tk.StringVar(value=str(self.config.rows_per_page))
        rows_spin = ttk.Spinbox(rows_frame, from_=1, to=10, textvariable=self.rows_var, width=10)
        rows_spin.pack(side=tk.RIGHT)
        rows_spin.bind('<KeyRelease>', lambda e: self.update_preview())
        
        # 保存按钮
        ttk.Button(left_frame, text="保存配置", command=self.save_config).pack(pady=20)
        
        # 右侧预览面板
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        ttk.Label(right_frame, text="预览:", font=("微软雅黑", 12)).pack(anchor=tk.W)
        
        # 预览画布
        canvas_frame = ttk.Frame(right_frame, relief=tk.SUNKEN, borderwidth=2)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.canvas = tk.Canvas(canvas_frame, bg="white", width=self.preview_width, height=self.preview_height)
        self.canvas.pack(expand=True)
    
    def update_preview(self):
        """更新预览"""
        try:
            # 创建预览图像
            img = Image.new('RGB', (self.preview_width, self.preview_height), 'white')
            draw = ImageDraw.Draw(img)
            
            # 获取配置
            text = self.text_widget.get("1.0", tk.END).strip()
            if not text:
                text = "预览文本"
            
            font_size = int(self.size_var.get() or 24)
            opacity = int(self.opacity_var.get())
            rows = int(self.rows_var.get() or 3)
            
            # 加载字体
            try:
                font = ImageFont.truetype("C:/Windows/Fonts/STKAITI.TTF", font_size)
            except:
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/simkai.ttf", font_size)
                except:
                    font = ImageFont.load_default()
            
            # 计算位置
            margin = 30
            available_height = self.preview_height - 2 * margin
            row_height = available_height / rows
            
            # 绘制水印
            for row in range(rows):
                y = margin + row * row_height + row_height / 2
                
                # 交错排列
                if row % 2 == 0:
                    x = margin + 50
                else:
                    x = self.preview_width - margin - 100
                
                # 绘制多行文本
                lines = text.split('\n')
                line_height = font_size + 5
                
                for i, line in enumerate(lines):
                    if line.strip():
                        color = (128, 128, 128, int(255 * opacity / 100))
                        
                        # 创建临时图像用于透明度
                        temp_img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))
                        temp_draw = ImageDraw.Draw(temp_img)
                        temp_draw.text((0, i * line_height), line, font=font, fill=color)
                        
                        # 旋转45度
                        temp_img = temp_img.rotate(45, expand=True)
                        
                        # 粘贴到主图像
                        img.paste(temp_img, (int(x), int(y)), temp_img)
            
            # 显示预览
            self.preview_photo = ImageTk.PhotoImage(img)
            self.canvas.delete("all")
            self.canvas.create_image(self.preview_width//2, self.preview_height//2, image=self.preview_photo)
            
        except Exception as e:
            print(f"预览更新失败: {e}")
            self.canvas.delete("all")
            self.canvas.create_text(self.preview_width//2, self.preview_height//2, 
                                  text=f"预览失败: {str(e)}", fill="red")
    
    def save_config(self):
        """保存配置"""
        try:
            self.config.text = self.text_widget.get("1.0", tk.END).strip()
            self.config.font_size = int(self.size_var.get() or 24)
            self.config.opacity = self.opacity_var.get() / 100
            self.config.rows_per_page = int(self.rows_var.get() or 3)
            
            # 重新初始化
            self.config.__post_init__()
            
            messagebox.showinfo("成功", "配置已保存！")
            print(f"✅ 配置已保存: {self.config.text[:50]}...")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")
    
    def run(self):
        self.root.mainloop()

def main():
    print("🚀 启动简单水印编辑器...")
    app = SimpleWatermarkEditor()
    app.run()
    print("✅ 编辑器已关闭")

if __name__ == "__main__":
    main()
