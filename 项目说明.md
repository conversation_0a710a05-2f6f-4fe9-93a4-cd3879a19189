# PDF水印添加工具 - 项目说明

## 项目概述

这是一个功能完整的PDF水印添加工具，完全按照您的要求开发，支持批量处理和中文字体显示。

## 实现的功能

### ✅ 核心功能
1. **批量或单个导入PDF文件** - 支持文件选择对话框和目录批量处理
2. **添加自定义水印** - 完全按照您的要求配置水印参数
3. **PDF转图片型PDF** - 处理后自动转换为图片型PDF
4. **中文字体检测** - 自动检测和应用楷体字体

### ✅ 水印配置（完全按要求实现）
- **内容**：小红书ID：11491392150 关注保研喵学姐 更多免费资料分享
- **字体**：楷体
- **字号**：54
- **旋转角度**：45度
- **不透明度**：20%
- **相对页面比例**：50%
- **多行水印**：一页三行
- **位置**：垂直居中11毫米，水平对齐垂直居中

### ✅ 技术特性
- 模块化设计，代码结构清晰
- 完整的错误处理和用户提示
- 自动字体检测和配置
- 批量处理进度显示
- 文件验证和路径管理
- 完整的测试用例

## 项目结构

```
pdf_watermark_tool/
├── src/                    # 源代码目录
│   ├── config/            # 配置模块
│   │   └── watermark_config.py    # 水印配置类
│   ├── core/              # 核心处理模块
│   │   ├── pdf_processor.py       # PDF处理器
│   │   └── watermark_engine.py    # 水印引擎
│   ├── utils/             # 工具模块
│   │   ├── file_manager.py        # 文件管理器
│   │   └── font_detector.py       # 字体检测器
│   └── ui/                # 用户界面
│       └── cli.py                  # 命令行界面
├── fonts/                 # 字体文件目录
├── tests/                 # 测试目录
│   └── test_watermark.py          # 测试用例
├── examples/              # 示例文件
│   └── demo.py                     # 演示脚本
├── main.py               # 主程序入口
├── install.py            # 自动安装脚本
├── requirements.txt      # 依赖包列表
└── README.md             # 项目说明
```

## 快速开始

### 1. 安装依赖
```bash
# 自动安装（推荐）
python install.py

# 或手动安装
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python main.py
```

### 3. 选择处理方式
- 单个PDF文件
- 多个PDF文件
- 整个目录的PDF文件

### 4. 查看结果
程序会在原文件目录生成：
- `原文件名_watermarked.pdf` - 添加水印的PDF
- `原文件名_watermarked_image.pdf` - 图片型PDF（最终文件）

## 主要模块说明

### 1. 水印配置模块 (`watermark_config.py`)
- 定义所有水印参数
- 支持配置验证和单位转换
- 自动检测字体路径

### 2. 水印引擎 (`watermark_engine.py`)
- 生成水印PDF
- 计算水印位置
- 处理字体渲染和透明度

### 3. PDF处理器 (`pdf_processor.py`)
- 读取和处理PDF文件
- 应用水印到每一页
- 转换为图片型PDF
- 批量处理支持

### 4. 文件管理器 (`file_manager.py`)
- 文件选择对话框
- 批量文件处理
- 路径验证和管理

### 5. 字体检测器 (`font_detector.py`)
- 自动检测系统楷体字体
- 字体兼容性测试
- 中文字符支持验证

### 6. 命令行界面 (`cli.py`)
- 用户友好的菜单系统
- 进度显示和错误提示
- 配置查看和修改

## 测试和验证

### 运行测试
```bash
python tests/test_watermark.py
```

### 运行演示
```bash
python examples/demo.py
```

### 测试内容
- 水印配置验证
- 字体检测功能
- PDF处理流程
- 批量处理功能
- 完整工作流程测试

## 技术栈

- **Python 3.7+** - 主要开发语言
- **PyPDF2** - PDF文件读取和操作
- **reportlab** - PDF生成和水印创建
- **PyMuPDF (fitz)** - PDF转图片处理
- **Pillow** - 图像处理
- **fonttools** - 字体文件处理
- **tkinter** - 文件选择对话框

## 特色功能

### 1. 智能字体检测
- 自动搜索系统中的楷体字体
- 支持多种楷体字体文件
- 字体兼容性测试
- 中文字符支持验证

### 2. 精确水印定位
- 按毫米精确定位
- 支持旋转和透明度
- 多行文本布局
- 页面比例自适应

### 3. 批量处理优化
- 进度显示
- 错误处理和恢复
- 文件验证
- 磁盘空间检查

### 4. 用户体验优化
- 直观的菜单系统
- 详细的操作提示
- 错误信息和解决建议
- 配置查看和验证

## 注意事项

1. **字体要求**：建议安装楷体字体以获得最佳效果
2. **磁盘空间**：确保有足够空间存储输出文件
3. **文件权限**：确保对目标目录有写入权限
4. **Python版本**：建议使用Python 3.7或更高版本

## 故障排除

### 常见问题
1. **字体显示异常** - 检查是否安装了楷体字体
2. **文件处理失败** - 检查PDF文件是否损坏或加密
3. **权限错误** - 确保对目标目录有写入权限
4. **内存不足** - 处理大文件时可能需要更多内存

### 解决方案
- 运行字体检测功能检查字体状态
- 使用小文件测试功能是否正常
- 检查错误日志获取详细信息
- 联系技术支持获取帮助

## 项目完成度

✅ **100%完成** - 所有要求的功能都已实现并测试通过

- ✅ 批量和单个PDF文件导入
- ✅ 自定义水印添加（完全按要求配置）
- ✅ PDF转图片型PDF
- ✅ 中文字体检测和应用
- ✅ 用户友好的界面
- ✅ 完整的测试用例
- ✅ 详细的文档说明

这个项目是一个完整的、生产就绪的PDF水印添加工具，完全满足您的所有要求！
