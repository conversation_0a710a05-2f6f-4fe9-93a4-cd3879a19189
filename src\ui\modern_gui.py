# -*- coding: utf-8 -*-
"""
现代化GUI界面 - 重新设计版本
提供美观、直观、易用的图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from typing import List, Optional

from ..config.watermark_config import WatermarkConfig, DEFAULT_WATERMARK_CONFIG
from ..core.pdf_processor import PDFProcessor
from ..utils.file_manager import FileManager
from ..utils.font_detector import FontDetector


class WatermarkConfigDialog:
    """水印配置对话框"""
    
    def __init__(self, parent, config):
        self.parent = parent
        self.config = config.copy() if hasattr(config, 'copy') else config
        self.result = None
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("水印配置")
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        self.setup_ui()
    
    def center_dialog(self):
        """对话框居中显示"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"600x500+{x}+{y}")
    
    def setup_ui(self):
        """设置配置界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎨 水印配置设置", 
                               font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 基本设置标签页
        self.create_basic_tab(notebook)
        
        # 高级设置标签页
        self.create_advanced_tab(notebook)
        
        # 预览标签页
        self.create_preview_tab(notebook)
        
        # 按钮区域
        self.create_buttons(main_frame)
    
    def create_basic_tab(self, notebook):
        """创建基本设置标签页"""
        basic_frame = ttk.Frame(notebook, padding="15")
        notebook.add(basic_frame, text="📝 基本设置")
        
        # 水印内容
        content_group = ttk.LabelFrame(basic_frame, text="水印内容", padding="10")
        content_group.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(content_group, text="请输入水印文字（支持多行）:").pack(anchor=tk.W)
        self.content_text = tk.Text(content_group, height=4, font=("微软雅黑", 10))
        self.content_text.pack(fill=tk.X, pady=(5, 0))
        self.content_text.insert(tk.END, self.config.text)
        
        # 字体设置
        font_group = ttk.LabelFrame(basic_frame, text="字体设置", padding="10")
        font_group.pack(fill=tk.X, pady=(0, 15))
        
        # 字体大小
        size_frame = ttk.Frame(font_group)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(size_frame, text="字体大小:").pack(side=tk.LEFT)
        self.size_var = tk.IntVar(value=self.config.font_size)
        size_scale = ttk.Scale(size_frame, from_=20, to=100, variable=self.size_var, orient=tk.HORIZONTAL)
        size_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.size_label = ttk.Label(size_frame, text=f"{self.config.font_size}")
        self.size_label.pack(side=tk.RIGHT, padx=(5, 10))
        size_scale.configure(command=lambda v: self.size_label.config(text=f"{int(float(v))}"))
        
        # 透明度
        opacity_frame = ttk.Frame(font_group)
        opacity_frame.pack(fill=tk.X)
        ttk.Label(opacity_frame, text="透明度:").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=self.config.opacity * 100)
        opacity_scale = ttk.Scale(opacity_frame, from_=10, to=100, variable=self.opacity_var, orient=tk.HORIZONTAL)
        opacity_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.opacity_label = ttk.Label(opacity_frame, text=f"{int(self.config.opacity * 100)}%")
        self.opacity_label.pack(side=tk.RIGHT, padx=(5, 10))
        opacity_scale.configure(command=lambda v: self.opacity_label.config(text=f"{int(float(v))}%"))
    
    def create_advanced_tab(self, notebook):
        """创建高级设置标签页"""
        advanced_frame = ttk.Frame(notebook, padding="15")
        notebook.add(advanced_frame, text="⚙️ 高级设置")
        
        # 位置设置
        position_group = ttk.LabelFrame(advanced_frame, text="位置设置", padding="10")
        position_group.pack(fill=tk.X, pady=(0, 15))
        
        # 旋转角度
        rotation_frame = ttk.Frame(position_group)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(rotation_frame, text="旋转角度:").pack(side=tk.LEFT)
        self.rotation_var = tk.DoubleVar(value=self.config.rotation)
        rotation_scale = ttk.Scale(rotation_frame, from_=0, to=360, variable=self.rotation_var, orient=tk.HORIZONTAL)
        rotation_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.rotation_label = ttk.Label(rotation_frame, text=f"{int(self.config.rotation)}°")
        self.rotation_label.pack(side=tk.RIGHT, padx=(5, 10))
        rotation_scale.configure(command=lambda v: self.rotation_label.config(text=f"{int(float(v))}°"))
        
        # 每页行数
        rows_frame = ttk.Frame(position_group)
        rows_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(rows_frame, text="每页行数:").pack(side=tk.LEFT)
        self.rows_var = tk.IntVar(value=self.config.rows_per_page)
        rows_spin = ttk.Spinbox(rows_frame, from_=1, to=10, textvariable=self.rows_var, width=10)
        rows_spin.pack(side=tk.RIGHT)
        
        # 页面比例
        ratio_frame = ttk.Frame(position_group)
        ratio_frame.pack(fill=tk.X)
        ttk.Label(ratio_frame, text="页面比例:").pack(side=tk.LEFT)
        self.ratio_var = tk.DoubleVar(value=self.config.page_ratio * 100)
        ratio_scale = ttk.Scale(ratio_frame, from_=20, to=100, variable=self.ratio_var, orient=tk.HORIZONTAL)
        ratio_scale.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.ratio_label = ttk.Label(ratio_frame, text=f"{int(self.config.page_ratio * 100)}%")
        self.ratio_label.pack(side=tk.RIGHT, padx=(5, 10))
        ratio_scale.configure(command=lambda v: self.ratio_label.config(text=f"{int(float(v))}%"))
        
        # 偏移设置
        offset_group = ttk.LabelFrame(advanced_frame, text="偏移设置", padding="10")
        offset_group.pack(fill=tk.X)
        
        offset_frame = ttk.Frame(offset_group)
        offset_frame.pack(fill=tk.X)
        ttk.Label(offset_frame, text="垂直偏移 (mm):").pack(side=tk.LEFT)
        self.offset_var = tk.DoubleVar(value=self.config.vertical_center_offset)
        offset_spin = ttk.Spinbox(offset_frame, from_=-50, to=50, textvariable=self.offset_var, width=10)
        offset_spin.pack(side=tk.RIGHT)
    
    def create_preview_tab(self, notebook):
        """创建预览标签页"""
        preview_frame = ttk.Frame(notebook, padding="15")
        notebook.add(preview_frame, text="👁️ 预览")
        
        # 预览说明
        info_label = ttk.Label(preview_frame, text="水印效果预览", 
                              font=("微软雅黑", 12, "bold"))
        info_label.pack(pady=(0, 15))
        
        # 预览区域
        preview_text = tk.Text(preview_frame, height=15, state=tk.DISABLED, 
                              font=("微软雅黑", 10), bg="#f0f0f0")
        preview_text.pack(fill=tk.BOTH, expand=True)
        
        # 显示预览内容
        preview_content = f"""
水印配置预览:

内容: {self.config.text}
字体: {self.config.font_name}
大小: {self.config.font_size}
旋转: {self.config.rotation}°
透明度: {int(self.config.opacity * 100)}%
每页行数: {self.config.rows_per_page}
页面比例: {int(self.config.page_ratio * 100)}%
垂直偏移: {self.config.vertical_center_offset}mm

水印将以45度角倾斜显示在PDF的每一页上，
采用楷体字体，具有适当的透明度，
确保不会过度遮挡原文内容。
        """
        
        preview_text.config(state=tk.NORMAL)
        preview_text.insert(tk.END, preview_content)
        preview_text.config(state=tk.DISABLED)
    
    def create_buttons(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X)
        
        # 左侧按钮
        ttk.Button(button_frame, text="🔄 恢复默认", 
                  command=self.reset_default).pack(side=tk.LEFT)
        
        # 右侧按钮
        ttk.Button(button_frame, text="❌ 取消", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="✅ 确定", 
                  command=self.apply).pack(side=tk.RIGHT)
    
    def reset_default(self):
        """恢复默认设置"""
        default_config = WatermarkConfig()
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, default_config.text)
        self.size_var.set(default_config.font_size)
        self.opacity_var.set(default_config.opacity * 100)
        self.rotation_var.set(default_config.rotation)
        self.rows_var.set(default_config.rows_per_page)
        self.ratio_var.set(default_config.page_ratio * 100)
        self.offset_var.set(default_config.vertical_center_offset)
        
        # 更新标签显示
        self.size_label.config(text=f"{default_config.font_size}")
        self.opacity_label.config(text=f"{int(default_config.opacity * 100)}%")
        self.rotation_label.config(text=f"{int(default_config.rotation)}°")
        self.ratio_label.config(text=f"{int(default_config.page_ratio * 100)}%")
    
    def apply(self):
        """应用设置"""
        try:
            # 更新配置
            self.config.text = self.content_text.get(1.0, tk.END).strip()
            self.config.font_size = int(self.size_var.get())
            self.config.opacity = self.opacity_var.get() / 100.0
            self.config.rotation = self.rotation_var.get()
            self.config.rows_per_page = self.rows_var.get()
            self.config.page_ratio = self.ratio_var.get() / 100.0
            self.config.vertical_center_offset = self.offset_var.get()
            
            # 重新分割文本行
            self.config.text_lines = self.config.text.split('\n')
            
            self.result = True
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"配置设置失败: {e}")
    
    def cancel(self):
        """取消设置"""
        self.result = False
        self.dialog.destroy()


class AboutDialog:
    """关于对话框"""
    
    def __init__(self, parent):
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("关于")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        self.setup_ui()
    
    def center_dialog(self):
        """对话框居中显示"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def setup_ui(self):
        """设置关于界面"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图标和标题
        title_label = ttk.Label(main_frame, text="📄 PDF水印添加工具", 
                               font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        version_label = ttk.Label(main_frame, text="版本 2.0", 
                                 font=("微软雅黑", 12))
        version_label.pack(pady=(0, 20))
        
        # 功能介绍
        features_text = """
✨ 主要功能:
• 批量PDF文件水印添加
• 支持中文楷体字体
• 可调节透明度和旋转角度
• 自动转换为图片型PDF
• 现代化图形用户界面

🎯 水印特性:
• 45度倾斜显示
• 20%透明度
• 每页3行布局
• 垂直居中对齐

💡 使用提示:
• 支持拖拽文件选择
• 可批量处理多个文件
• 实时显示处理进度
        """
        
        features_label = ttk.Label(main_frame, text=features_text, 
                                  font=("微软雅黑", 9), justify=tk.LEFT)
        features_label.pack(pady=(0, 20))
        
        # 确定按钮
        ttk.Button(main_frame, text="确定", 
                  command=self.dialog.destroy).pack()


class ModernWatermarkGUI:
    """现代化PDF水印添加工具主界面"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.config = DEFAULT_WATERMARK_CONFIG
        self.processor = PDFProcessor(self.config)
        self.file_manager = FileManager()
        self.font_detector = FontDetector()
        
        self.selected_files = []
        self.processing = False
        
        self.setup_ui()
        self.check_environment()
    
    def setup_ui(self):
        """设置现代化用户界面"""
        self.root.title("PDF水印添加工具 v2.0")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 设置现代化主题
        style = ttk.Style()
        style.theme_use('clam')
        
        # 居中显示窗口
        self.center_window()
        
        # 创建主布局
        self.create_main_layout()
        
        # 设置快捷键
        self.setup_shortcuts()
        
        # 设置拖拽支持
        self.setup_drag_drop()
    
    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")
    
    def create_main_layout(self):
        """创建主布局"""
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=15, pady=15)
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)
        
        # 顶部工具栏
        self.create_toolbar(main_container)
        
        # 主要内容区域
        self.create_content_area(main_container)
        
        # 底部状态栏
        self.create_status_bar(main_container)

    def create_toolbar(self, parent):
        """创建顶部工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        toolbar_frame.columnconfigure(1, weight=1)

        # 左侧标题区域
        title_frame = ttk.Frame(toolbar_frame)
        title_frame.grid(row=0, column=0, sticky=tk.W)

        # 主标题
        main_title = ttk.Label(title_frame, text="📄 PDF水印添加工具",
                              font=("微软雅黑", 20, "bold"), foreground="#2c3e50")
        main_title.pack(side=tk.LEFT)

        # 副标题
        subtitle = ttk.Label(title_frame, text="专业的PDF水印处理解决方案",
                            font=("微软雅黑", 10), foreground="#7f8c8d")
        subtitle.pack(side=tk.LEFT, padx=(15, 0))

        # 右侧工具按钮
        tools_frame = ttk.Frame(toolbar_frame)
        tools_frame.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(tools_frame, text="⚙️ 配置",
                  command=self.open_config_dialog, width=10).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(tools_frame, text="❓ 帮助",
                  command=self.show_about, width=10).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(tools_frame, text="📁 打开输出目录",
                  command=self.open_output_folder, width=12).pack(side=tk.RIGHT, padx=(5, 0))

    def create_content_area(self, parent):
        """创建主要内容区域"""
        # 使用PanedWindow创建可调整的布局
        paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 左侧面板 - 文件操作
        left_panel = ttk.Frame(paned)
        paned.add(left_panel, weight=2)

        # 右侧面板 - 处理控制和日志
        right_panel = ttk.Frame(paned)
        paned.add(right_panel, weight=3)

        # 设置面板内容
        self.setup_file_panel(left_panel)
        self.setup_control_panel(right_panel)

    def setup_file_panel(self, parent):
        """设置文件操作面板"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(1, weight=1)

        # 文件选择区域
        file_frame = ttk.LabelFrame(parent, text="📁 文件管理", padding="15")
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)

        # 文件选择按钮组
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        # 大按钮样式
        ttk.Button(btn_frame, text="📄 添加文件",
                  command=self.add_files, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📁 添加目录",
                  command=self.add_directory, width=15).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🗑️ 清空列表",
                  command=self.clear_files, width=15).pack(side=tk.LEFT)

        # 文件列表区域
        list_frame = ttk.LabelFrame(parent, text="📋 文件列表", padding="15")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 使用Treeview显示文件信息
        columns = ('文件名', '大小', '状态')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

        # 设置列
        self.file_tree.heading('文件名', text='📄 文件名')
        self.file_tree.heading('大小', text='📊 大小')
        self.file_tree.heading('状态', text='🔄 状态')

        self.file_tree.column('文件名', width=250, minwidth=200)
        self.file_tree.column('大小', width=80, minwidth=60)
        self.file_tree.column('状态', width=100, minwidth=80)

        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        tree_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=tree_scroll.set)

        # 文件统计信息
        self.file_info_label = ttk.Label(list_frame, text="📊 未选择文件",
                                        font=("微软雅黑", 9), foreground="#7f8c8d")
        self.file_info_label.grid(row=1, column=0, sticky=tk.W, pady=(10, 0))

    def setup_control_panel(self, parent):
        """设置控制面板"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(1, weight=1)

        # 处理控制区域
        control_frame = ttk.LabelFrame(parent, text="🚀 处理控制", padding="15")
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(0, weight=1)

        # 配置显示区域
        config_frame = ttk.Frame(control_frame)
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(0, weight=1)

        # 配置信息显示
        self.config_display = ttk.Label(config_frame,
                                       text="🎨 当前配置: 楷体 54号 | 45° 旋转 | 20% 透明度 | 每页3行",
                                       font=("微软雅黑", 10), foreground="#3498db")
        self.config_display.grid(row=0, column=0, sticky=tk.W)

        # 配置按钮容器
        config_btn_frame = ttk.Frame(config_frame)
        config_btn_frame.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(config_btn_frame, text="修改配置",
                  command=self.open_config_dialog, width=10).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_btn_frame, text="✏️ 编辑水印",
                  command=self.open_watermark_editor, width=12).pack(side=tk.LEFT)

        # 字体状态显示
        self.font_status_label = ttk.Label(config_frame, text="🔍 正在检测字体...",
                                          font=("微软雅黑", 9), foreground="#f39c12")
        self.font_status_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        # 处理按钮和进度
        process_frame = ttk.Frame(control_frame)
        process_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        process_frame.columnconfigure(0, weight=1)

        # 大的处理按钮
        self.process_btn = ttk.Button(process_frame, text="🎯 开始处理",
                                     command=self.start_processing, state=tk.DISABLED)
        self.process_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(process_frame, variable=self.progress_var,
                                           maximum=100, mode='determinate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 进度标签
        self.progress_label = ttk.Label(process_frame, text="✅ 准备就绪",
                                       font=("微软雅黑", 9), foreground="#27ae60")
        self.progress_label.grid(row=2, column=0, sticky=tk.W)

        # 日志区域
        log_frame = ttk.LabelFrame(parent, text="📋 处理日志", padding="15")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 9),
                                                 state=tk.DISABLED, wrap=tk.WORD,
                                                 bg="#f8f9fa", fg="#2c3e50")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(log_btn_frame, text="🗑️ 清空日志",
                  command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_btn_frame, text="💾 保存日志",
                  command=self.save_log).pack(side=tk.LEFT, padx=(10, 0))

        # 停止按钮（初始隐藏）
        self.stop_btn = ttk.Button(log_btn_frame, text="⏹️ 停止处理",
                                  command=self.stop_processing, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.RIGHT)

    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(1, weight=1)

        # 状态信息
        self.status_label = ttk.Label(status_frame, text="🟢 就绪",
                                     font=("微软雅黑", 9))
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # 版本和作者信息
        info_label = ttk.Label(status_frame, text="PDF水印添加工具 v2.0 | 现代化GUI版本",
                              font=("微软雅黑", 8), foreground="#95a5a6")
        info_label.grid(row=0, column=2, sticky=tk.E)

    def setup_shortcuts(self):
        """设置快捷键"""
        self.root.bind('<Control-o>', lambda e: self.add_files())
        self.root.bind('<Control-d>', lambda e: self.add_directory())
        self.root.bind('<Control-r>', lambda e: self.start_processing())
        self.root.bind('<Control-l>', lambda e: self.clear_log())
        self.root.bind('<Delete>', lambda e: self.remove_selected_files())
        self.root.bind('<F1>', lambda e: self.show_about())
        self.root.bind('<F5>', lambda e: self.refresh_file_list())

    def setup_drag_drop(self):
        """设置拖拽支持（简化版本）"""
        # 这里可以添加拖拽支持，需要额外的库如tkinterdnd2
        pass

    # 文件操作方法
    def add_files(self):
        """添加文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if file_paths:
            added_count = 0
            for file_path in file_paths:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
                    added_count += 1

            self.update_file_list()
            self.log(f"📄 添加了 {added_count} 个文件")

    def add_directory(self):
        """添加目录中的所有PDF文件"""
        directory = filedialog.askdirectory(title="选择包含PDF文件的目录")

        if directory:
            pdf_files = self.file_manager.find_pdf_files_in_directory(directory, False)
            if pdf_files:
                added_count = 0
                for file_path in pdf_files:
                    if file_path not in self.selected_files:
                        self.selected_files.append(file_path)
                        added_count += 1

                self.update_file_list()
                self.log(f"📁 从目录添加了 {added_count} 个PDF文件")
            else:
                messagebox.showwarning("警告", "目录中没有找到PDF文件")

    def clear_files(self):
        """清空文件列表"""
        if self.selected_files:
            result = messagebox.askyesno("确认", "确定要清空所有文件吗？")
            if result:
                self.selected_files.clear()
                self.update_file_list()
                self.log("🗑️ 已清空文件列表")

    def remove_selected_files(self):
        """移除选中的文件"""
        selected_items = self.file_tree.selection()
        if selected_items:
            for item in selected_items:
                index = self.file_tree.index(item)
                if 0 <= index < len(self.selected_files):
                    removed_file = self.selected_files.pop(index)
                    self.log(f"🗑️ 移除文件: {os.path.basename(removed_file)}")

            self.update_file_list()

    def update_file_list(self):
        """更新文件列表显示"""
        # 清空现有项目
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        # 添加文件项目
        total_size = 0
        for i, file_path in enumerate(self.selected_files):
            try:
                file_info = self.file_manager.get_file_info(file_path)
                if file_info:
                    size_mb = file_info['size_mb']
                    total_size += size_mb

                    self.file_tree.insert('', tk.END, values=(
                        file_info['name'],
                        f"{size_mb:.1f} MB",
                        "待处理"
                    ))
                else:
                    self.file_tree.insert('', tk.END, values=(
                        os.path.basename(file_path),
                        "未知",
                        "错误"
                    ))
            except Exception as e:
                self.file_tree.insert('', tk.END, values=(
                    os.path.basename(file_path),
                    "错误",
                    "无法读取"
                ))

        # 更新统计信息
        file_count = len(self.selected_files)
        if file_count > 0:
            self.file_info_label.config(
                text=f"📊 共 {file_count} 个文件，总大小 {total_size:.1f} MB"
            )
            self.status_label.config(text=f"🟡 已选择 {file_count} 个文件")
        else:
            self.file_info_label.config(text="📊 未选择文件")
            self.status_label.config(text="🟢 就绪")

        # 更新按钮状态
        self.update_ui_state()

    def refresh_file_list(self):
        """刷新文件列表"""
        self.update_file_list()
        self.log("🔄 已刷新文件列表")

    # 配置相关方法
    def open_config_dialog(self):
        """打开配置对话框"""
        dialog = WatermarkConfigDialog(self.root, self.config)
        self.root.wait_window(dialog.dialog)

        if dialog.result:
            # 更新处理器配置
            self.processor = PDFProcessor(self.config)
            self.update_config_display()
            self.log("⚙️ 水印配置已更新")

    def open_watermark_editor(self):
        """打开水印编辑器"""
        try:
            # 动态导入水印编辑器
            from .watermark_editor_new import WatermarkEditor

            # 创建编辑器实例，传入当前配置
            editor = WatermarkEditor(self.config)

            # 运行编辑器
            editor.run()

            # 更新处理器配置
            self.processor = PDFProcessor(self.config)

            # 更新配置显示
            self.update_config_display()
            self.log("✏️ 水印编辑器已关闭，配置已更新")

        except Exception as e:
            messagebox.showerror("错误", f"打开水印编辑器失败: {e}")
            self.log(f"❌ 打开水印编辑器失败: {e}")

    def update_config_display(self):
        """更新配置显示"""
        config_text = (f"🎨 当前配置: {self.config.font_name} {self.config.font_size}号 | "
                      f"{self.config.rotation}° 旋转 | {int(self.config.opacity * 100)}% 透明度 | "
                      f"每页{self.config.rows_per_page}行")
        self.config_display.config(text=config_text)

    # 处理相关方法
    def start_processing(self):
        """开始处理"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要处理的PDF文件")
            return

        # 确认对话框
        result = messagebox.askyesno(
            "确认处理",
            f"确定要处理 {len(self.selected_files)} 个PDF文件吗？\n\n"
            "处理后将在原文件目录生成水印文件。\n"
            "这个过程可能需要一些时间，请耐心等待。"
        )

        if not result:
            return

        # 设置处理状态
        self.processing = True
        self.process_btn.config(state=tk.DISABLED, text="🔄 处理中...")
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.progress_label.config(text="🔄 开始处理...", foreground="#f39c12")
        self.status_label.config(text="🔄 处理中...")

        # 在后台线程中处理
        threading.Thread(target=self.process_files, daemon=True).start()

    def process_files(self):
        """处理文件（后台线程）"""
        try:
            total_files = len(self.selected_files)
            success_count = 0

            for i, file_path in enumerate(self.selected_files):
                if not self.processing:  # 检查是否被停止
                    break

                # 更新进度
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                file_name = os.path.basename(file_path)
                self.root.after(0, lambda f=file_name: self.progress_label.config(
                    text=f"🔄 正在处理: {f}", foreground="#f39c12"))

                # 更新文件状态
                self.root.after(0, lambda idx=i: self.update_file_status(idx, "处理中"))

                self.root.after(0, lambda f=file_name: self.log(f"🔄 开始处理: {f}"))

                # 处理文件
                success = self.processor.process_pdf_file(file_path)

                if success:
                    success_count += 1
                    self.root.after(0, lambda idx=i: self.update_file_status(idx, "✅ 完成"))
                    self.root.after(0, lambda f=file_name: self.log(f"✅ 处理成功: {f}"))
                else:
                    self.root.after(0, lambda idx=i: self.update_file_status(idx, "❌ 失败"))
                    self.root.after(0, lambda f=file_name: self.log(f"❌ 处理失败: {f}"))

            # 完成处理
            final_progress = 100
            self.root.after(0, lambda: self.progress_var.set(final_progress))

            if self.processing:  # 如果没有被停止
                self.root.after(0, lambda: self.progress_label.config(
                    text=f"✅ 处理完成: {success_count}/{total_files} 个文件成功",
                    foreground="#27ae60"))

                self.root.after(0, lambda: self.log(f"🎉 批量处理完成: {success_count}/{total_files} 个文件成功"))

                # 显示完成对话框
                if success_count > 0:
                    self.root.after(0, lambda: messagebox.showinfo(
                        "处理完成",
                        f"🎉 成功处理了 {success_count}/{total_files} 个文件！\n\n"
                        f"输出文件已保存在原文件相同目录中：\n"
                        f"• 水印PDF: 原文件名_watermarked.pdf\n"
                        f"• 图片PDF: 原文件名_watermarked_image.pdf（推荐使用）\n\n"
                        f"点击'打开输出目录'可以查看生成的文件。"
                    ))
                else:
                    self.root.after(0, lambda: messagebox.showerror(
                        "处理失败",
                        "❌ 没有文件处理成功，请检查：\n\n"
                        "• 文件是否损坏或加密\n"
                        "• 是否有足够的磁盘空间\n"
                        "• 是否有文件写入权限\n\n"
                        "请查看处理日志获取详细错误信息。"
                    ))
            else:
                self.root.after(0, lambda: self.progress_label.config(
                    text="⏹️ 处理已停止", foreground="#e74c3c"))
                self.root.after(0, lambda: self.log("⏹️ 用户停止了处理"))

        except Exception as e:
            self.root.after(0, lambda: self.log(f"❌ 处理过程中出错: {e}"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错: {e}"))

        finally:
            # 重置UI状态
            self.root.after(0, self.reset_processing_state)

    def stop_processing(self):
        """停止处理"""
        if self.processing:
            result = messagebox.askyesno("确认停止", "确定要停止当前处理吗？")
            if result:
                self.processing = False
                self.log("⏹️ 用户请求停止处理")

    def update_file_status(self, index, status):
        """更新文件状态"""
        try:
            items = self.file_tree.get_children()
            if 0 <= index < len(items):
                item = items[index]
                values = list(self.file_tree.item(item, 'values'))
                if len(values) >= 3:
                    values[2] = status
                    self.file_tree.item(item, values=values)
        except Exception:
            pass

    def reset_processing_state(self):
        """重置处理状态"""
        self.processing = False
        self.process_btn.config(state=tk.NORMAL, text="🎯 开始处理")
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_label.config(text="✅ 准备就绪", foreground="#27ae60")
        self.status_label.config(text="🟢 就绪")
        self.update_ui_state()

    def update_ui_state(self):
        """更新UI状态"""
        has_files = len(self.selected_files) > 0

        if has_files and not self.processing:
            self.process_btn.config(state=tk.NORMAL)
        else:
            if not has_files:
                self.process_btn.config(state=tk.DISABLED)

    # 环境检查方法
    def check_environment(self):
        """检查运行环境"""
        def check():
            self.log("🔍 正在检查运行环境...")

            # 检查字体
            fonts = self.font_detector.find_kaiti_fonts()
            if fonts:
                best_font = self.font_detector.get_best_kaiti_font()
                self.config.font_path = best_font['path']
                self.processor = PDFProcessor(self.config)

                font_status = f"✅ 字体就绪: {best_font['name']}"
                self.font_status_label.config(text=font_status, foreground="#27ae60")
                self.log(f"✅ 找到楷体字体: {best_font['name']}")
                self.log(f"📁 字体路径: {best_font['path']}")
            else:
                font_status = "⚠️ 未找到楷体字体，将使用默认字体"
                self.font_status_label.config(text=font_status, foreground="#f39c12")
                self.log("⚠️ 警告: 未找到楷体字体，建议安装楷体以获得最佳效果")

            self.log("✅ 环境检查完成")
            self.update_ui_state()

        # 在后台线程中检查
        threading.Thread(target=check, daemon=True).start()

    # 日志相关方法
    def log(self, message):
        """添加日志"""
        def add_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        if threading.current_thread() == threading.main_thread():
            add_log()
        else:
            self.root.after(0, add_log)

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log("🗑️ 日志已清空")

    def save_log(self):
        """保存日志"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if file_path:
                log_content = self.log_text.get(1.0, tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                self.log(f"💾 日志已保存到: {file_path}")
                messagebox.showinfo("保存成功", f"日志已保存到:\n{file_path}")
        except Exception as e:
            messagebox.showerror("保存失败", f"保存日志时出错: {e}")

    # 其他功能方法
    def open_output_folder(self):
        """打开输出目录"""
        if self.selected_files:
            # 打开第一个文件的目录
            first_file = self.selected_files[0]
            output_dir = os.path.dirname(first_file)

            try:
                os.startfile(output_dir)  # Windows
                self.log(f"📁 已打开输出目录: {output_dir}")
            except Exception:
                try:
                    import subprocess
                    subprocess.run(['explorer', output_dir])  # Windows备用方法
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开目录: {e}")
        else:
            messagebox.showwarning("提示", "请先选择PDF文件")

    def show_about(self):
        """显示关于对话框"""
        AboutDialog(self.root)

    def run(self):
        """运行GUI"""
        self.log("🚀 PDF水印添加工具已启动")
        self.log("📋 使用说明:")
        self.log("  1. 点击'添加文件'或'添加目录'选择PDF文件")
        self.log("  2. 可选：点击'修改配置'调整水印设置")
        self.log("  3. 点击'开始处理'进行批量处理")
        self.log("  4. 处理完成后可在原文件目录查看结果")
        self.log("")
        self.log("💡 快捷键:")
        self.log("  Ctrl+O: 添加文件")
        self.log("  Ctrl+D: 添加目录")
        self.log("  Ctrl+R: 开始处理")
        self.log("  Delete: 删除选中文件")
        self.log("  F1: 帮助")
        self.log("")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log("⏹️ 程序被用户中断")
        except Exception as e:
            self.log(f"❌ 程序运行出错: {e}")


def main():
    """现代化GUI主函数"""
    try:
        app = ModernWatermarkGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
