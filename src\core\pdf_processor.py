# -*- coding: utf-8 -*-
"""
PDF处理器模块
负责PDF文件的读取、处理和保存
"""

import os
import io
from typing import List, Optional, Tuple
from pathlib import Path
import fitz  # PyMuPDF
from PyPDF2 import PdfReader, PdfWriter
from pdf2image import convert_from_path
from PIL import Image

from ..config.watermark_config import WatermarkConfig
from .watermark_engine import WatermarkEngine


class PDFProcessor:
    """PDF处理器类"""
    
    def __init__(self, config: WatermarkConfig):
        """
        初始化PDF处理器
        
        Args:
            config: 水印配置对象
        """
        self.config = config
        self.watermark_engine = WatermarkEngine(config)
    
    def process_pdf_file(self, input_path: str, output_path: Optional[str] = None) -> bool:
        """
        处理单个PDF文件
        
        Args:
            input_path: 输入PDF文件路径
            output_path: 输出PDF文件路径（可选）
            
        Returns:
            处理是否成功
        """
        try:
            # 验证输入文件
            if not os.path.exists(input_path):
                print(f"输入文件不存在: {input_path}")
                return False
            
            if not input_path.lower().endswith('.pdf'):
                print(f"不是PDF文件: {input_path}")
                return False
            
            # 生成输出路径
            if not output_path:
                output_path = self._generate_output_path(input_path)
            
            print(f"开始处理: {input_path}")
            print(f"输出路径: {output_path}")
            
            # 添加水印
            success = self._add_watermark_to_pdf(input_path, output_path)
            
            if success:
                # 转换为图片型PDF
                image_pdf_path = self._generate_image_pdf_path(output_path)
                success = self._convert_to_image_pdf(output_path, image_pdf_path)
                
                if success:
                    print(f"处理完成: {image_pdf_path}")
                    return True
                else:
                    print(f"转换图片PDF失败: {output_path}")
                    return False
            else:
                print(f"添加水印失败: {input_path}")
                return False
                
        except Exception as e:
            print(f"处理PDF文件时出错: {e}")
            return False
    
    def _generate_output_path(self, input_path: str) -> str:
        """生成输出文件路径"""
        path = Path(input_path)
        return str(path.parent / f"{path.stem}_watermarked{path.suffix}")
    
    def _generate_image_pdf_path(self, watermarked_path: str) -> str:
        """生成图片PDF文件路径"""
        path = Path(watermarked_path)
        return str(path.parent / f"{path.stem}_image{path.suffix}")
    
    def _add_watermark_to_pdf(self, input_path: str, output_path: str) -> bool:
        """
        给PDF添加水印
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            # 读取PDF
            reader = PdfReader(input_path)
            writer = PdfWriter()
            
            # 处理每一页
            for page_num, page in enumerate(reader.pages):
                print(f"处理第 {page_num + 1} 页...")
                
                # 获取页面尺寸
                page_width = float(page.mediabox.width)
                page_height = float(page.mediabox.height)
                
                # 应用水印
                watermarked_page = self.watermark_engine.apply_watermark_to_page(
                    page, page_width, page_height
                )
                
                writer.add_page(watermarked_page)
            
            # 保存文件
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            
            print(f"水印添加完成: {output_path}")
            return True
            
        except Exception as e:
            print(f"添加水印时出错: {e}")
            return False

    def _convert_to_image_pdf(self, input_path: str, output_path: str) -> bool:
        """
        将PDF转换为图片型PDF

        Args:
            input_path: 输入PDF路径
            output_path: 输出PDF路径

        Returns:
            是否成功
        """
        try:
            print("开始转换为图片型PDF...")

            # 使用PyMuPDF进行转换（更高效）
            doc = fitz.open(input_path)

            # 创建新的PDF文档
            new_doc = fitz.open()

            for page_num in range(len(doc)):
                print(f"转换第 {page_num + 1} 页...")

                # 获取页面
                page = doc[page_num]

                # 设置渲染参数（高质量）
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放提高质量
                pix = page.get_pixmap(matrix=mat)

                # 转换为PIL图像
                img_data = pix.tobytes("png")
                img = Image.open(io.BytesIO(img_data))

                # 创建新页面
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)

                # 将图像插入新页面
                img_bytes = io.BytesIO()
                img.save(img_bytes, format='PNG')
                img_bytes.seek(0)

                new_page.insert_image(page.rect, stream=img_bytes.getvalue())

            # 保存新文档
            new_doc.save(output_path)
            new_doc.close()
            doc.close()

            print(f"图片型PDF转换完成: {output_path}")
            return True

        except Exception as e:
            print(f"转换图片PDF时出错: {e}")
            return False

    def process_multiple_files(self, file_paths: List[str]) -> Tuple[int, int]:
        """
        批量处理多个PDF文件

        Args:
            file_paths: PDF文件路径列表

        Returns:
            (成功数量, 总数量)
        """
        success_count = 0
        total_count = len(file_paths)

        print(f"开始批量处理 {total_count} 个文件...")

        for i, file_path in enumerate(file_paths, 1):
            print(f"\n[{i}/{total_count}] 处理文件: {file_path}")

            if self.process_pdf_file(file_path):
                success_count += 1
                print(f"✓ 成功处理")
            else:
                print(f"✗ 处理失败")

        print(f"\n批量处理完成: {success_count}/{total_count} 个文件成功")
        return success_count, total_count

    def validate_watermark_display(self, pdf_path: str) -> bool:
        """
        验证水印显示效果

        Args:
            pdf_path: PDF文件路径

        Returns:
            验证是否通过
        """
        try:
            print("验证水印显示效果...")

            # 使用PyMuPDF检查文本内容
            doc = fitz.open(pdf_path)

            for page_num in range(min(3, len(doc))):  # 检查前3页
                page = doc[page_num]
                text = page.get_text()

                # 检查是否包含水印文本
                watermark_lines = self.config.get_text_lines()
                found_lines = 0

                for line in watermark_lines:
                    if line.strip() in text:
                        found_lines += 1

                if found_lines >= len(watermark_lines) * 0.5:  # 至少找到一半的水印文本
                    print(f"✓ 第 {page_num + 1} 页水印验证通过")
                else:
                    print(f"✗ 第 {page_num + 1} 页水印验证失败")

            doc.close()
            return True

        except Exception as e:
            print(f"验证水印时出错: {e}")
            return False
