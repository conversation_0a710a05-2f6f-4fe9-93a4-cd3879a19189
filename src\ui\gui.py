# -*- coding: utf-8 -*-
"""
GUI界面模块
提供图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from typing import List, Optional

from ..config.watermark_config import WatermarkConfig, DEFAULT_WATERMARK_CONFIG
from ..core.pdf_processor import PDFProcessor
from ..utils.file_manager import FileManager
from ..utils.font_detector import FontDetector


class WatermarkGUI:
    """PDF水印添加工具GUI界面"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.config = DEFAULT_WATERMARK_CONFIG
        self.processor = PDFProcessor(self.config)
        self.file_manager = FileManager()
        self.font_detector = FontDetector()
        
        self.selected_files = []
        self.processing = False
        
        self.setup_ui()
        self.check_environment()
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("PDF水印添加工具 v1.0")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        try:
            self.root.iconbitmap(default="")  # 可以添加图标文件
        except:
            pass
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="PDF水印添加工具", 
                               font=("微软雅黑", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        self.setup_file_selection(main_frame, 1)
        
        # 水印配置区域
        self.setup_watermark_config(main_frame, 2)
        
        # 处理控制区域
        self.setup_processing_controls(main_frame, 3)
        
        # 日志输出区域
        self.setup_log_area(main_frame, 4)
        
        # 状态栏
        self.setup_status_bar(main_frame, 5)
    
    def setup_file_selection(self, parent, row):
        """设置文件选择区域"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 按钮区域
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(btn_frame, text="选择单个文件", 
                  command=self.select_single_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="选择多个文件", 
                  command=self.select_multiple_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="选择目录", 
                  command=self.select_directory).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="清空列表", 
                  command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件列表
        ttk.Label(file_frame, text="选中的文件:").grid(row=1, column=0, sticky=tk.W)
        
        # 创建文件列表框架
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 文件列表
        self.file_listbox = tk.Listbox(list_frame, height=6)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
    
    def setup_watermark_config(self, parent, row):
        """设置水印配置区域"""
        config_frame = ttk.LabelFrame(parent, text="水印配置", padding="10")
        config_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 水印内容显示
        ttk.Label(config_frame, text="水印内容:").grid(row=0, column=0, sticky=tk.W)
        content_text = tk.Text(config_frame, height=3, width=50, state=tk.DISABLED)
        content_text.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 显示水印内容
        content_text.config(state=tk.NORMAL)
        content_text.insert(tk.END, self.config.text)
        content_text.config(state=tk.DISABLED)
        
        # 配置信息
        config_info = [
            f"字体: {self.config.font_name}",
            f"字号: {self.config.font_size}",
            f"旋转: {self.config.rotation}°",
            f"透明度: {self.config.opacity * 100}%",
            f"页面比例: {self.config.page_ratio * 100}%",
            f"每页行数: {self.config.rows_per_page}"
        ]
        
        config_label = ttk.Label(config_frame, text=" | ".join(config_info))
        config_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 字体状态
        self.font_status_label = ttk.Label(config_frame, text="正在检测字体...")
        self.font_status_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def setup_processing_controls(self, parent, row):
        """设置处理控制区域"""
        control_frame = ttk.LabelFrame(parent, text="处理控制", padding="10")
        control_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮区域
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X)
        
        self.process_btn = ttk.Button(btn_frame, text="开始处理", 
                                     command=self.start_processing, state=tk.DISABLED)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(btn_frame, text="停止处理", 
                                  command=self.stop_processing, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, 
                                           maximum=100, length=300)
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))
        
        # 进度标签
        self.progress_label = ttk.Label(control_frame, text="准备就绪")
        self.progress_label.pack(pady=(5, 0))
    
    def setup_log_area(self, parent, row):
        """设置日志输出区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="10")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", 
                  command=self.clear_log).grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def setup_status_bar(self, parent, row):
        """设置状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v1.0", foreground="gray")
        version_label.grid(row=0, column=2, sticky=tk.E)
    
    def check_environment(self):
        """检查运行环境"""
        def check():
            self.log("正在检查运行环境...")
            
            # 检查字体
            fonts = self.font_detector.find_kaiti_fonts()
            if fonts:
                best_font = self.font_detector.get_best_kaiti_font()
                self.config.font_path = best_font['path']
                self.processor = PDFProcessor(self.config)
                
                font_status = f"✓ 字体就绪: {best_font['name']}"
                self.font_status_label.config(text=font_status, foreground="green")
                self.log(f"找到楷体字体: {best_font['name']}")
            else:
                font_status = "⚠ 未找到楷体字体，将使用默认字体"
                self.font_status_label.config(text=font_status, foreground="orange")
                self.log("警告: 未找到楷体字体")
            
            self.log("环境检查完成")
            self.update_ui_state()
        
        # 在后台线程中检查
        threading.Thread(target=check, daemon=True).start()
    
    def select_single_file(self):
        """选择单个文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.selected_files = [file_path]
            self.update_file_list()
            self.log(f"选择文件: {os.path.basename(file_path)}")
    
    def select_multiple_files(self):
        """选择多个文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择PDF文件（可多选）",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_paths:
            self.selected_files = list(file_paths)
            self.update_file_list()
            self.log(f"选择了 {len(file_paths)} 个文件")
    
    def select_directory(self):
        """选择目录"""
        directory = filedialog.askdirectory(title="选择包含PDF文件的目录")
        
        if directory:
            pdf_files = self.file_manager.find_pdf_files_in_directory(directory, False)
            if pdf_files:
                self.selected_files = pdf_files
                self.update_file_list()
                self.log(f"从目录选择了 {len(pdf_files)} 个PDF文件")
            else:
                messagebox.showwarning("警告", "目录中没有找到PDF文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.selected_files = []
        self.update_file_list()
        self.log("已清空文件列表")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            self.file_listbox.insert(tk.END, os.path.basename(file_path))
        
        self.update_ui_state()
    
    def update_ui_state(self):
        """更新UI状态"""
        has_files = len(self.selected_files) > 0
        
        if has_files and not self.processing:
            self.process_btn.config(state=tk.NORMAL)
            self.status_label.config(text=f"准备处理 {len(self.selected_files)} 个文件")
        else:
            self.process_btn.config(state=tk.DISABLED)
            if not has_files:
                self.status_label.config(text="请选择要处理的PDF文件")
    
    def start_processing(self):
        """开始处理"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要处理的PDF文件")
            return
        
        # 确认对话框
        result = messagebox.askyesno(
            "确认处理", 
            f"确定要处理 {len(self.selected_files)} 个PDF文件吗？\n\n"
            "处理后将在原文件目录生成水印文件。"
        )
        
        if not result:
            return
        
        self.processing = True
        self.process_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        
        # 在后台线程中处理
        threading.Thread(target=self.process_files, daemon=True).start()
    
    def process_files(self):
        """处理文件（后台线程）"""
        try:
            total_files = len(self.selected_files)
            success_count = 0
            
            for i, file_path in enumerate(self.selected_files):
                if not self.processing:  # 检查是否被停止
                    break
                
                # 更新进度
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda f=file_path: self.progress_label.config(
                    text=f"正在处理: {os.path.basename(f)}"))
                
                self.root.after(0, lambda f=file_path: self.log(f"开始处理: {os.path.basename(f)}"))
                
                # 处理文件
                success = self.processor.process_pdf_file(file_path)
                
                if success:
                    success_count += 1
                    self.root.after(0, lambda f=file_path: self.log(f"✓ 处理成功: {os.path.basename(f)}"))
                else:
                    self.root.after(0, lambda f=file_path: self.log(f"✗ 处理失败: {os.path.basename(f)}"))
            
            # 完成处理
            final_progress = 100
            self.root.after(0, lambda: self.progress_var.set(final_progress))
            self.root.after(0, lambda: self.progress_label.config(
                text=f"处理完成: {success_count}/{total_files} 个文件成功"))
            
            self.root.after(0, lambda: self.log(f"批量处理完成: {success_count}/{total_files} 个文件成功"))
            
            # 显示完成对话框
            if success_count > 0:
                self.root.after(0, lambda: messagebox.showinfo(
                    "处理完成", 
                    f"成功处理了 {success_count}/{total_files} 个文件！\n\n"
                    "请检查原文件目录中的输出文件。"
                ))
            else:
                self.root.after(0, lambda: messagebox.showerror(
                    "处理失败", 
                    "没有文件处理成功，请检查文件是否有效。"
                ))
        
        except Exception as e:
            self.root.after(0, lambda: self.log(f"处理过程中出错: {e}"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错: {e}"))
        
        finally:
            # 重置UI状态
            self.root.after(0, self.reset_processing_state)
    
    def stop_processing(self):
        """停止处理"""
        self.processing = False
        self.log("用户停止了处理")
    
    def reset_processing_state(self):
        """重置处理状态"""
        self.processing = False
        self.process_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.update_ui_state()
    
    def log(self, message):
        """添加日志"""
        def add_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        
        if threading.current_thread() == threading.main_thread():
            add_log()
        else:
            self.root.after(0, add_log)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def run(self):
        """运行GUI"""
        self.log("PDF水印添加工具已启动")
        self.log("请选择要处理的PDF文件，然后点击'开始处理'")
        self.root.mainloop()


def main():
    """GUI主函数"""
    app = WatermarkGUI()
    app.run()


if __name__ == "__main__":
    main()
