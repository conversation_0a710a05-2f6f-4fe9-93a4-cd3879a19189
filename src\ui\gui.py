# -*- coding: utf-8 -*-
"""
现代化GUI界面模块
提供美观易用的图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from typing import List, Optional

from ..config.watermark_config import WatermarkConfig, DEFAULT_WATERMARK_CONFIG
from ..core.pdf_processor import PDFProcessor
from ..utils.file_manager import FileManager
from ..utils.font_detector import FontDetector


class ConfigWindow:
    """水印配置窗口"""

    def __init__(self, parent, config):
        self.parent = parent
        self.config = config
        self.result = None

        self.window = tk.Toplevel(parent)
        self.window.title("水印配置")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()

        # 居中显示
        self.center_window()
        self.setup_ui()

    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.window.winfo_screenheight() // 2) - (400 // 2)
        self.window.geometry(f"500x400+{x}+{y}")

    def setup_ui(self):
        """设置配置界面"""
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="水印配置设置",
                               font=("微软雅黑", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 水印内容
        content_frame = ttk.LabelFrame(main_frame, text="水印内容", padding="10")
        content_frame.pack(fill=tk.X, pady=(0, 15))

        self.content_text = tk.Text(content_frame, height=4, font=("微软雅黑", 10))
        self.content_text.pack(fill=tk.X)
        self.content_text.insert(tk.END, self.config.text)

        # 基本设置
        basic_frame = ttk.LabelFrame(main_frame, text="基本设置", padding="10")
        basic_frame.pack(fill=tk.X, pady=(0, 15))

        # 字体大小
        size_frame = ttk.Frame(basic_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(size_frame, text="字体大小:").pack(side=tk.LEFT)
        self.size_var = tk.IntVar(value=self.config.font_size)
        size_spin = ttk.Spinbox(size_frame, from_=20, to=100, textvariable=self.size_var, width=10)
        size_spin.pack(side=tk.RIGHT)

        # 旋转角度
        rotation_frame = ttk.Frame(basic_frame)
        rotation_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(rotation_frame, text="旋转角度:").pack(side=tk.LEFT)
        self.rotation_var = tk.DoubleVar(value=self.config.rotation)
        rotation_spin = ttk.Spinbox(rotation_frame, from_=0, to=360, textvariable=self.rotation_var, width=10)
        rotation_spin.pack(side=tk.RIGHT)

        # 透明度
        opacity_frame = ttk.Frame(basic_frame)
        opacity_frame.pack(fill=tk.X, pady=(0, 10))
        ttk.Label(opacity_frame, text="透明度 (%):").pack(side=tk.LEFT)
        self.opacity_var = tk.DoubleVar(value=self.config.opacity * 100)
        opacity_spin = ttk.Spinbox(opacity_frame, from_=10, to=100, textvariable=self.opacity_var, width=10)
        opacity_spin.pack(side=tk.RIGHT)

        # 每页行数
        rows_frame = ttk.Frame(basic_frame)
        rows_frame.pack(fill=tk.X)
        ttk.Label(rows_frame, text="每页行数:").pack(side=tk.LEFT)
        self.rows_var = tk.IntVar(value=self.config.rows_per_page)
        rows_spin = ttk.Spinbox(rows_frame, from_=1, to=10, textvariable=self.rows_var, width=10)
        rows_spin.pack(side=tk.RIGHT)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        ttk.Button(button_frame, text="恢复默认", command=self.reset_default).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="取消", command=self.cancel).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="确定", command=self.apply).pack(side=tk.RIGHT)

    def reset_default(self):
        """恢复默认设置"""
        default_config = WatermarkConfig()
        self.content_text.delete(1.0, tk.END)
        self.content_text.insert(tk.END, default_config.text)
        self.size_var.set(default_config.font_size)
        self.rotation_var.set(default_config.rotation)
        self.opacity_var.set(default_config.opacity * 100)
        self.rows_var.set(default_config.rows_per_page)

    def apply(self):
        """应用设置"""
        try:
            # 更新配置
            self.config.text = self.content_text.get(1.0, tk.END).strip()
            self.config.font_size = self.size_var.get()
            self.config.rotation = self.rotation_var.get()
            self.config.opacity = self.opacity_var.get() / 100.0
            self.config.rows_per_page = self.rows_var.get()

            # 重新分割文本行
            self.config.text_lines = self.config.text.split('\n')

            self.result = True
            self.window.destroy()
        except Exception as e:
            messagebox.showerror("错误", f"配置设置失败: {e}")

    def cancel(self):
        """取消设置"""
        self.result = False
        self.window.destroy()


class WatermarkGUI:
    """PDF水印添加工具主界面"""

    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.config = DEFAULT_WATERMARK_CONFIG
        self.processor = PDFProcessor(self.config)
        self.file_manager = FileManager()
        self.font_detector = FontDetector()

        self.selected_files = []
        self.processing = False

        self.setup_ui()
        self.check_environment()

    def setup_ui(self):
        """设置现代化用户界面"""
        self.root.title("PDF水印添加工具 v2.0")
        self.root.geometry("900x650")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 居中显示窗口
        self.center_window()

        # 创建主框架
        self.create_main_layout()

        # 设置快捷键
        self.setup_shortcuts()

    def center_window(self):
        """窗口居中"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (900 // 2)
        y = (self.root.winfo_screenheight() // 2) - (650 // 2)
        self.root.geometry(f"900x650+{x}+{y}")

    def create_main_layout(self):
        """创建主布局"""
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # 主容器
        main_container = ttk.Frame(self.root)
        main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        main_container.columnconfigure(1, weight=1)
        main_container.rowconfigure(1, weight=1)

        # 顶部标题栏
        self.create_header(main_container)

        # 主要内容区域
        self.create_content_area(main_container)

        # 底部状态栏
        self.create_status_bar(main_container)

    def create_header(self, parent):
        """创建顶部标题栏"""
        header_frame = ttk.Frame(parent)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        header_frame.columnconfigure(1, weight=1)

        # 标题和图标
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky=tk.W)

        title_label = ttk.Label(title_frame, text="📄 PDF水印添加工具",
                               font=("微软雅黑", 18, "bold"))
        title_label.pack(side=tk.LEFT)

        subtitle_label = ttk.Label(title_frame, text="专业的PDF水印处理工具",
                                  font=("微软雅黑", 10), foreground="gray")
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # 右侧按钮
        button_frame = ttk.Frame(header_frame)
        button_frame.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(button_frame, text="⚙️ 配置", command=self.open_config).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="❓ 帮助", command=self.show_help).pack(side=tk.RIGHT, padx=(5, 0))

    def create_content_area(self, parent):
        """创建主要内容区域"""
        # 使用PanedWindow创建可调整的布局
        paned = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        paned.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 左侧面板
        left_panel = ttk.Frame(paned)
        paned.add(left_panel, weight=1)

        # 右侧面板
        right_panel = ttk.Frame(paned)
        paned.add(right_panel, weight=1)

        # 设置左侧内容
        self.setup_left_panel(left_panel)

        # 设置右侧内容
        self.setup_right_panel(right_panel)

    def setup_left_panel(self, parent):
        """设置左侧面板"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(1, weight=1)

        # 文件选择区域
        file_frame = ttk.LabelFrame(parent, text="📁 文件选择", padding="15")
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(0, weight=1)

        # 文件选择按钮组
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="📄 选择文件",
                  command=self.select_files, width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="📁 选择目录",
                  command=self.select_directory, width=12).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="🗑️ 清空",
                  command=self.clear_files, width=8).pack(side=tk.RIGHT)

        # 文件列表
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 使用Treeview显示文件列表
        columns = ('文件名', '大小', '状态')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        # 设置列标题
        self.file_tree.heading('文件名', text='文件名')
        self.file_tree.heading('大小', text='大小')
        self.file_tree.heading('状态', text='状态')

        # 设置列宽
        self.file_tree.column('文件名', width=200)
        self.file_tree.column('大小', width=80)
        self.file_tree.column('状态', width=80)

        self.file_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        tree_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_tree.configure(yscrollcommand=tree_scroll.set)

        # 文件统计
        self.file_count_label = ttk.Label(file_frame, text="未选择文件", foreground="gray")
        self.file_count_label.grid(row=2, column=0, sticky=tk.W)

        # 处理控制区域
        control_frame = ttk.LabelFrame(parent, text="🚀 处理控制", padding="15")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        control_frame.columnconfigure(0, weight=1)

        # 快速配置显示
        config_display = ttk.Frame(control_frame)
        config_display.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))

        self.config_label = ttk.Label(config_display, text="水印配置: 楷体 54号 45° 20%透明",
                                     font=("微软雅黑", 9), foreground="blue")
        self.config_label.pack(side=tk.LEFT)

        ttk.Button(config_display, text="修改", command=self.open_config, width=8).pack(side=tk.RIGHT)

        # 处理按钮
        self.process_btn = ttk.Button(control_frame, text="🎯 开始处理",
                                     command=self.start_processing, state=tk.DISABLED)
        self.process_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # 进度显示
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var,
                                           maximum=100)
        self.progress_bar.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.progress_label = ttk.Label(control_frame, text="准备就绪", foreground="green")
        self.progress_label.grid(row=3, column=0, sticky=tk.W)

    def setup_right_panel(self, parent):
        """设置右侧面板"""
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # 日志和状态区域
        log_frame = ttk.LabelFrame(parent, text="📋 处理日志", padding="15")
        log_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 9),
                                                 state=tk.DISABLED, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(log_btn_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_btn_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))

        # 字体状态显示
        self.font_status_frame = ttk.Frame(log_btn_frame)
        self.font_status_frame.pack(side=tk.RIGHT)

        self.font_status_label = ttk.Label(self.font_status_frame, text="🔍 检测字体中...",
                                          foreground="orange")
        self.font_status_label.pack()

    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(1, weight=1)

        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)

        # 版本和作者信息
        info_label = ttk.Label(status_frame, text="v2.0 | PDF水印添加工具", foreground="gray")
        info_label.grid(row=0, column=2, sticky=tk.E)

    def setup_shortcuts(self):
        """设置快捷键"""
        self.root.bind('<Control-o>', lambda e: self.select_files())
        self.root.bind('<Control-d>', lambda e: self.select_directory())
        self.root.bind('<Control-r>', lambda e: self.start_processing())
        self.root.bind('<F1>', lambda e: self.show_help())
        self.root.bind('<F5>', lambda e: self.clear_log())
    
    def setup_file_selection(self, parent, row):
        """设置文件选择区域"""
        # 文件选择框架
        file_frame = ttk.LabelFrame(parent, text="文件选择", padding="10")
        file_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 按钮区域
        btn_frame = ttk.Frame(file_frame)
        btn_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(btn_frame, text="选择单个文件", 
                  command=self.select_single_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="选择多个文件", 
                  command=self.select_multiple_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="选择目录", 
                  command=self.select_directory).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="清空列表", 
                  command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
        
        # 文件列表
        ttk.Label(file_frame, text="选中的文件:").grid(row=1, column=0, sticky=tk.W)
        
        # 创建文件列表框架
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 文件列表
        self.file_listbox = tk.Listbox(list_frame, height=6)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
    
    def setup_watermark_config(self, parent, row):
        """设置水印配置区域"""
        config_frame = ttk.LabelFrame(parent, text="水印配置", padding="10")
        config_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 水印内容显示
        ttk.Label(config_frame, text="水印内容:").grid(row=0, column=0, sticky=tk.W)
        content_text = tk.Text(config_frame, height=3, width=50, state=tk.DISABLED)
        content_text.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 显示水印内容
        content_text.config(state=tk.NORMAL)
        content_text.insert(tk.END, self.config.text)
        content_text.config(state=tk.DISABLED)
        
        # 配置信息
        config_info = [
            f"字体: {self.config.font_name}",
            f"字号: {self.config.font_size}",
            f"旋转: {self.config.rotation}°",
            f"透明度: {self.config.opacity * 100}%",
            f"页面比例: {self.config.page_ratio * 100}%",
            f"每页行数: {self.config.rows_per_page}"
        ]
        
        config_label = ttk.Label(config_frame, text=" | ".join(config_info))
        config_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 字体状态
        self.font_status_label = ttk.Label(config_frame, text="正在检测字体...")
        self.font_status_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def setup_processing_controls(self, parent, row):
        """设置处理控制区域"""
        control_frame = ttk.LabelFrame(parent, text="处理控制", padding="10")
        control_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 按钮区域
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X)
        
        self.process_btn = ttk.Button(btn_frame, text="开始处理", 
                                     command=self.start_processing, state=tk.DISABLED)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(btn_frame, text="停止处理", 
                                  command=self.stop_processing, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, 
                                           maximum=100, length=300)
        self.progress_bar.pack(fill=tk.X, pady=(10, 0))
        
        # 进度标签
        self.progress_label = ttk.Label(control_frame, text="准备就绪")
        self.progress_label.pack(pady=(5, 0))
    
    def setup_log_area(self, parent, row):
        """设置日志输出区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="10")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", 
                  command=self.clear_log).grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def setup_status_bar(self, parent, row):
        """设置状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(1, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 版本信息
        version_label = ttk.Label(status_frame, text="v1.0", foreground="gray")
        version_label.grid(row=0, column=2, sticky=tk.E)
    
    def check_environment(self):
        """检查运行环境"""
        def check():
            self.log("正在检查运行环境...")
            
            # 检查字体
            fonts = self.font_detector.find_kaiti_fonts()
            if fonts:
                best_font = self.font_detector.get_best_kaiti_font()
                self.config.font_path = best_font['path']
                self.processor = PDFProcessor(self.config)
                
                font_status = f"✓ 字体就绪: {best_font['name']}"
                self.font_status_label.config(text=font_status, foreground="green")
                self.log(f"找到楷体字体: {best_font['name']}")
            else:
                font_status = "⚠ 未找到楷体字体，将使用默认字体"
                self.font_status_label.config(text=font_status, foreground="orange")
                self.log("警告: 未找到楷体字体")
            
            self.log("环境检查完成")
            self.update_ui_state()
        
        # 在后台线程中检查
        threading.Thread(target=check, daemon=True).start()
    
    def select_single_file(self):
        """选择单个文件"""
        file_path = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.selected_files = [file_path]
            self.update_file_list()
            self.log(f"选择文件: {os.path.basename(file_path)}")
    
    def select_multiple_files(self):
        """选择多个文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择PDF文件（可多选）",
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )
        
        if file_paths:
            self.selected_files = list(file_paths)
            self.update_file_list()
            self.log(f"选择了 {len(file_paths)} 个文件")
    
    def select_directory(self):
        """选择目录"""
        directory = filedialog.askdirectory(title="选择包含PDF文件的目录")
        
        if directory:
            pdf_files = self.file_manager.find_pdf_files_in_directory(directory, False)
            if pdf_files:
                self.selected_files = pdf_files
                self.update_file_list()
                self.log(f"从目录选择了 {len(pdf_files)} 个PDF文件")
            else:
                messagebox.showwarning("警告", "目录中没有找到PDF文件")
    
    def clear_files(self):
        """清空文件列表"""
        self.selected_files = []
        self.update_file_list()
        self.log("已清空文件列表")
    
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            self.file_listbox.insert(tk.END, os.path.basename(file_path))
        
        self.update_ui_state()
    
    def update_ui_state(self):
        """更新UI状态"""
        has_files = len(self.selected_files) > 0
        
        if has_files and not self.processing:
            self.process_btn.config(state=tk.NORMAL)
            self.status_label.config(text=f"准备处理 {len(self.selected_files)} 个文件")
        else:
            self.process_btn.config(state=tk.DISABLED)
            if not has_files:
                self.status_label.config(text="请选择要处理的PDF文件")
    
    def start_processing(self):
        """开始处理"""
        if not self.selected_files:
            messagebox.showwarning("警告", "请先选择要处理的PDF文件")
            return
        
        # 确认对话框
        result = messagebox.askyesno(
            "确认处理", 
            f"确定要处理 {len(self.selected_files)} 个PDF文件吗？\n\n"
            "处理后将在原文件目录生成水印文件。"
        )
        
        if not result:
            return
        
        self.processing = True
        self.process_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        self.progress_var.set(0)
        
        # 在后台线程中处理
        threading.Thread(target=self.process_files, daemon=True).start()
    
    def process_files(self):
        """处理文件（后台线程）"""
        try:
            total_files = len(self.selected_files)
            success_count = 0
            
            for i, file_path in enumerate(self.selected_files):
                if not self.processing:  # 检查是否被停止
                    break
                
                # 更新进度
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda f=file_path: self.progress_label.config(
                    text=f"正在处理: {os.path.basename(f)}"))
                
                self.root.after(0, lambda f=file_path: self.log(f"开始处理: {os.path.basename(f)}"))
                
                # 处理文件
                success = self.processor.process_pdf_file(file_path)
                
                if success:
                    success_count += 1
                    self.root.after(0, lambda f=file_path: self.log(f"✓ 处理成功: {os.path.basename(f)}"))
                else:
                    self.root.after(0, lambda f=file_path: self.log(f"✗ 处理失败: {os.path.basename(f)}"))
            
            # 完成处理
            final_progress = 100
            self.root.after(0, lambda: self.progress_var.set(final_progress))
            self.root.after(0, lambda: self.progress_label.config(
                text=f"处理完成: {success_count}/{total_files} 个文件成功"))
            
            self.root.after(0, lambda: self.log(f"批量处理完成: {success_count}/{total_files} 个文件成功"))
            
            # 显示完成对话框
            if success_count > 0:
                self.root.after(0, lambda: messagebox.showinfo(
                    "处理完成", 
                    f"成功处理了 {success_count}/{total_files} 个文件！\n\n"
                    "请检查原文件目录中的输出文件。"
                ))
            else:
                self.root.after(0, lambda: messagebox.showerror(
                    "处理失败", 
                    "没有文件处理成功，请检查文件是否有效。"
                ))
        
        except Exception as e:
            self.root.after(0, lambda: self.log(f"处理过程中出错: {e}"))
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错: {e}"))
        
        finally:
            # 重置UI状态
            self.root.after(0, self.reset_processing_state)
    
    def stop_processing(self):
        """停止处理"""
        self.processing = False
        self.log("用户停止了处理")
    
    def reset_processing_state(self):
        """重置处理状态"""
        self.processing = False
        self.process_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.update_ui_state()
    
    def log(self, message):
        """添加日志"""
        def add_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, f"{message}\n")
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)
        
        if threading.current_thread() == threading.main_thread():
            add_log()
        else:
            self.root.after(0, add_log)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
    
    def run(self):
        """运行GUI"""
        self.log("PDF水印添加工具已启动")
        self.log("请选择要处理的PDF文件，然后点击'开始处理'")
        self.root.mainloop()


def main():
    """GUI主函数"""
    app = WatermarkGUI()
    app.run()


if __name__ == "__main__":
    main()
